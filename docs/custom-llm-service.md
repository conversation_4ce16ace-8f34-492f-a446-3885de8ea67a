# Custom LLM Service

This service provides a way to connect to custom LLM API endpoints that follow the OpenAI API format.

## Features

- Connect to any OpenAI-compatible API endpoint
- Supports both OpenAI SDK and direct axios HTTP requests
- Automatic fallback from SDK to direct HTTP if SDK fails
- Configurable endpoints and parameters
- Simple interface for sending messages and creating chat completions

## Usage

### Basic Usage

```javascript
const customLLMService = require('../lib/util/customLLMService');

// Set the default endpoint (optional)
customLLMService.setDefaultEndpoint('http://your-api-endpoint:8000/v1');

// Send a simple message
async function sendMessage() {
  try {
    const response = await customLLMService.sendMessage(
      'Your message here',
      'model-name'
    );
    console.log('Response:', response);
  } catch (error) {
    console.error('Error:', error.message);
  }
}
```

### Advanced Usage

```javascript
const customLLMService = require('../lib/util/customLLMService');

// Create a chat completion with advanced options
async function createChatCompletion() {
  try {
    const completion = await customLLMService.createChatCompletion({
      model: 'model-name',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Tell me about yourself.' }
      ],
      max_tokens: 500,
      temperature: 0.7,
      top_p: 0.9
    });
    
    console.log('Completion:', completion.choices[0].message.content);
  } catch (error) {
    console.error('Error:', error.message);
  }
}
```

### Using with Different Endpoints

```javascript
const customLLMService = require('../lib/util/customLLMService');

// Send a message to a specific endpoint
async function sendToSpecificEndpoint() {
  try {
    const response = await customLLMService.sendMessage(
      'Your message here',
      'model-name',
      { temperature: 0.5 }, // Optional parameters
      'http://specific-endpoint:8000/v1' // Specific endpoint
    );
    console.log('Response:', response);
  } catch (error) {
    console.error('Error:', error.message);
  }
}
```

## API Reference

### `setDefaultEndpoint(url)`

Sets the default API endpoint for all requests.

- `url` (string): The base URL for the API endpoint

### `getClient(baseURL, apiKey)`

Gets or creates an OpenAI client for the specified endpoint.

- `baseURL` (string, optional): The base URL for the API endpoint
- `apiKey` (string, optional): API key for authentication

### `createChatCompletion(options, baseURL)`

Creates a chat completion using the specified options.

- `options` (object): Chat completion options (same as OpenAI API)
- `baseURL` (string, optional): The base URL for the API endpoint

### `sendMessage(message, model, options, baseURL)`

Convenience method to send a message and get a response.

- `message` (string): The message to send
- `model` (string): The model to use
- `options` (object, optional): Additional options
- `baseURL` (string, optional): The base URL for the API endpoint

## Examples

See the `examples/custom-llm-integration.js` file for more examples of how to use this service.
