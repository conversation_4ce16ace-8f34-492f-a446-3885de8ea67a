const customLLMService = require('../lib/util/customLLMService');

async function main() {
  try {
    console.log('Sending request to custom LLM API...');
    
    // Example 1: Using the convenience method
    const response1 = await customLLMService.sendMessage(
      '<PERSON>n chào, viết bài văn tả con mèo 10000 từ?',
      'meta-llama/Llama-3.3-70B-Instruct'
    );
    
    console.log('Response from convenience method:');
    console.log(response1);
    
    // Example 2: Using the more flexible createChatCompletion method
    const response2 = await customLLMService.createChatCompletion({
      model: 'meta-llama/Llama-3.3-70B-Instruct',
      messages: [
        { role: 'user', content: 'Xin chào, viết bài văn tả con mèo 10000 từ?' }
      ],
      max_tokens: 512,
      temperature: 0.7,
      top_p: 0.9
    });
    
    console.log('\nResponse from createChatCompletion:');
    console.log(response2.choices[0].message.content);
    
  } catch (error) {
    console.error('Error in example:', error);
  }
}

main();
