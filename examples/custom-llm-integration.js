/**
 * Example of integrating the customLLMService into an application
 */
const customLLMService = require('../lib/util/customLLMService');

// Example usage in an application
async function exampleUsage() {
  // Configure the service with your preferred endpoint
  customLLMService.setDefaultEndpoint('http://27.72.59.13:8000/v1');
  
  // Example 1: Simple message sending
  try {
    console.log('Example 1: Simple message sending');
    const response = await customLLMService.sendMessage(
      'Xin chào, viết bài văn tả con mèo 10000 từ?',
      'meta-llama/Llama-3.3-70B-Instruct'
    );
    console.log('Response:', response.substring(0, 100) + '...');
  } catch (error) {
    console.error('Example 1 failed:', error.message);
  }
  
  // Example 2: Advanced options
  try {
    console.log('\nExample 2: Advanced options');
    const completion = await customLLMService.createChatCompletion({
      model: 'meta-llama/Llama-3.3-70B-Instruct',
      messages: [
        { role: 'system', content: '<PERSON>ạn là một trợ lý AI hữu ích.' },
        { role: 'user', content: 'Viết một bài thơ ngắn về Hải Phòng.' }
      ],
      max_tokens: 256,
      temperature: 0.8,
      top_p: 0.95
    });
    
    console.log('Completion:', completion.choices[0].message.content);
  } catch (error) {
    console.error('Example 2 failed:', error.message);
  }
  
  // Example 3: Using with a different endpoint
  try {
    console.log('\nExample 3: Using with a different endpoint');
    const alternativeEndpoint = 'http://localhost:11434/v1'; // Example: local Ollama
    
    const response = await customLLMService.sendMessage(
      'Tóm tắt lịch sử Việt Nam trong 3 câu.',
      'meta-llama/Llama-3.3-70B-Instruct',
      { temperature: 0.3 }, // Lower temperature for more focused response
      alternativeEndpoint
    );
    
    console.log('Response from alternative endpoint:', response);
  } catch (error) {
    console.error('Example 3 failed:', error.message);
  }
}

// Run the examples
exampleUsage()
  .then(() => console.log('Examples completed'))
  .catch(err => console.error('Error running examples:', err));
