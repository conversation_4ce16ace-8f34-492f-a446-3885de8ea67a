// Test script for JSON parsing fix

// Test case 1: Normal JSON
const normalJson = '{"query": "who is the president of the USA"}';
console.log("Test case 1: Normal JSON");
try {
  const parsed = JSON.parse(normalJson);
  console.log("Success:", parsed);
} catch (error) {
  console.error("Error:", error);
}

// Test case 2: Concatenated JSON (the problematic case)
const concatenatedJson = '{"query": "t\\u1ed5ng th\\u1ed1ng m\\u1ef9 hi\\u1ec7n nay l\\u00e0 ai"}{"query": "tổng thống mỹ hiện nay là ai"}';
console.log("\nTest case 2: Concatenated JSON (original error case)");

// Original approach (will fail)
try {
  const parsed = JSON.parse(concatenatedJson);
  console.log("Success (unexpected):", parsed);
} catch (error) {
  console.error("Error (expected):", error.message);
}

// Our fixed approach
console.log("\nTest case 2 with fix:");
try {
  // Extract only the first valid JSON object
  let fixedJson = concatenatedJson;
  if (fixedJson.includes('}{')) {
    console.log("Detected multiple concatenated JSON objects, fixing...");
    fixedJson = fixedJson.substring(0, fixedJson.indexOf('}')+1);
  }
  console.log("Fixed JSON:", fixedJson);
  const parsed = JSON.parse(fixedJson);
  console.log("Success:", parsed);
} catch (error) {
  console.error("Error:", error);
}

// Test case 3: Malformed JSON with extra characters
const malformedJson = '{"query": "test query"} extra text';
console.log("\nTest case 3: Malformed JSON with extra characters");

// Original approach (will fail)
try {
  const parsed = JSON.parse(malformedJson);
  console.log("Success (unexpected):", parsed);
} catch (error) {
  console.error("Error (expected):", error.message);
}

// Our fixed approach
console.log("\nTest case 3 with fix:");
try {
  // Clean up the JSON string
  let fixedJson = malformedJson.trim();
  if (fixedJson.includes('}')) {
    console.log("Detected extra content after JSON, fixing...");
    fixedJson = fixedJson.substring(0, fixedJson.indexOf('}')+1);
  }
  console.log("Fixed JSON:", fixedJson);
  const parsed = JSON.parse(fixedJson);
  console.log("Success:", parsed);
} catch (error) {
  console.error("Error:", error);
}
