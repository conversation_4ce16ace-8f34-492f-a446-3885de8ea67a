const customLLMService = require('./lib/util/customLLMService');

async function main() {
  try {
    // Set the API endpoint
    customLLMService.setDefaultEndpoint('http://***********:8000/v1');

    console.log('Testing connection to custom LLM API...');

    // Try with different endpoints to see which one works
    const endpoints = [
      'http://***********:8000/v1',
      'http://localhost:8000/v1',
      'http://localhost:11434/v1'  // Ollama default
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);

        const response = await customLLMService.sendMessage(
          'Xin chào, viết bài văn tả con mèo 10000 từ?',
          'meta-llama/Llama-3.3-70B-Instruct',
          {},  // Default options
          endpoint
        );

        console.log('Response received:');
        console.log(response);
        console.log('Success with endpoint:', endpoint);
        break;
      } catch (error) {
        console.error(`Failed with endpoint ${endpoint}:`, error.message);
      }
    }
  } catch (error) {
    console.error('Error in test:', error.message);
  }
}

main();
