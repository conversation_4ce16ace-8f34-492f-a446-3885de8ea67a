{"name": "ss-clean-service", "version": "1.0.0", "description": "Use for handling HeyClean service", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "babel-node index.js --presets es2015"}, "repository": {"type": "git", "url": "git+https://github.com/sanship2017/ss-clean-service.git"}, "author": "tuanhm", "license": "ISC", "bugs": {"url": "https://github.com/sanship2017/ss-clean-service.git/issues"}, "homepage": "https://github.com/sanship2017/ss-clean-service.git#README", "dependencies": {"@google/generative-ai": "^0.24.0", "async": "2.6.4", "axios": "^0.21.4", "bcrypt": "^5.1.1", "cachegoose": "8.0.0", "config": "1.31.0", "connect-redis": "5.2.0", "cors": "2.8.5", "crypto": "1.0.1", "dotenv": "16.0.1", "escape-string-regexp": "4.0.0", "exceljs": "^4.4.0", "express": "4.18.1", "express-session": "1.17.3", "geolib": "3.3.4", "groq-sdk": "^0.12.0", "hoek": "4.2.1", "ioredis": "4.28.5", "isemail": "3.2.0", "joi": "10.6.0", "joi-objectid": "2.0.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.12", "langdetect": "^0.2.1", "lodash": "4.17.21", "moment": "2.29.4", "mongoose": "5.13.14", "ms": "2.1.3", "nanoid": "1.2.5", "node-cache": "^5.1.2", "node-schedule": "^2.1.1", "nodemailer": "3.1.8", "openai": "^5.12.0", "redis": "2.8.0", "request": "2.88.2", "request-promise": "4.2.6", "rr": "^0.1.0", "socket.io": "^2.0.4", "topo": "3.0.3", "translate-google": "^1.5.0", "validator": "13.1.17", "winston": "2.4.6", "winston-daily-rotate-file": "3.10.0", "xml2json": "0.11.2"}}