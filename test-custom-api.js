const { OpenAI } = require('openai');
const config = require('config');

// Create a custom OpenAI client pointing to the local API endpoint
const openai = new OpenAI({
  apiKey: 'dummy-key', // The API doesn't seem to require authentication, but the SDK requires a non-empty string
  baseURL: 'http://27.72.59.13:8000/v1',
  timeout: 60000
});

async function testCustomAPI() {
  try {
    console.log('Sending request to custom API endpoint...');
    
    const completion = await openai.chat.completions.create({
      model: 'meta-llama/Llama-3.3-70B-Instruct',
      messages: [
        { role: 'user', content: 'Xin chào, viết bài văn tả con mèo 10000 từ?' }
      ],
      max_tokens: 512,
      temperature: 0.7,
      top_p: 0.9
    });

    console.log('Response received:');
    console.log(completion.choices[0].message.content);
    
    return completion;
  } catch (error) {
    console.error('Error calling custom API:', error);
    throw error;
  }
}

// Execute the test
testCustomAPI()
  .then(() => console.log('Test completed successfully'))
  .catch(err => console.error('Test failed:', err));
