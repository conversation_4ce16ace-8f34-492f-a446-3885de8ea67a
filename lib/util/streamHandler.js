const translationService = require('./translationService');

class StreamHandler {
  constructor(io, deviceId) {
    this.io = io;
    this.deviceId = deviceId;
    this.buffer = [];
    this.batchSize = 3;
    this.batchTimeout = 100;
  }

  initialize() {
    this.startBatchProcessing();
  }

  async processBatch() {
    if (this.buffer.length === 0) return;

    const batch = this.buffer.splice(0, this.batchSize);
    const translatedBatch = await Promise.all(
      batch.map(({ chunk, type }) =>
        translationService.translateText(chunk)
          .then(translated => ({ translated, type }))
      )
    );

    translatedBatch.forEach(({ translated, type }) => {
      this.emitChunk(translated, type);
    });
  }

  emitChunk(chunk, type) {
    const eventMap = {
      reasoning: {
        event: 'newReasoning',
        processor: this.cleanReasoning.bind(this)
      },
      content: {
        event: 'newMessage',
        processor: this.cleanContent.bind(this)
      }
    };

    const { event, processor } = eventMap[type];
    const processedChunk = processor(chunk);

    this.io.to(this.deviceId).emit(event, {
      chunk: processedChunk,
      timestamp: Date.now()
    });
  }

  cleanReasoning(chunk) {
    return chunk
      .replace(/undefined/gi, '')
      .replace(/trợ lý:/gi, '')
      .replace(/assistant:/gi, '');
  }

  cleanContent(chunk) {
    return chunk
      .replace(/user:/gi, '')
      .replace(/<\|im_end\|>/g, '');
  }

  startBatchProcessing() {
    setInterval(() => this.processBatch(), this.batchTimeout);
  }

  addToBuffer(chunk, type) {
    this.buffer.push({ chunk, type });
  }
}

module.exports = StreamHandler;