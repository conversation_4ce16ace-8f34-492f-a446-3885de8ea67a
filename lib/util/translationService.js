const translate = require('translate-google')
const NodeCache = require('node-cache')
const QueueHandler = require('./queueHandler')

const translationCache = new NodeCache({ stdTTL: 600 })
const queue = new QueueHandler()

class TranslationService {
  async translateText(text) {
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);
    if (!hasNonLatinChars) return text;

    if (translationCache.has(text)) {
      return translationCache.get(text)
    }

    return queue.enqueue(async () => {
      try {
        const translated = await translate(text, { to: 'vi' });
        const translatedText = ' ' + translated.toLowerCase();
        translationCache.set(text, translatedText);
        return translatedText;
      } catch (error) {
        console.error(`Lỗi dịch: ${text}`, error);
        return text;
      }
    })
  };
}

module.exports = new TranslationService()