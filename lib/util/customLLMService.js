const { OpenAI } = require('openai');
const axios = require('axios');

// Cache for OpenAI clients
const clients = new Map();

/**
 * Service for connecting to custom LLM API endpoints using the OpenAI SDK format
 */
class CustomLLMService {
  constructor() {
    // Default API endpoint
    this.defaultEndpoint = 'http://27.72.59.13:8000/v1';
  }

  /**
   * Set the default API endpoint
   * @param {string} url - The base URL for the API
   */
  setDefaultEndpoint(url) {
    this.defaultEndpoint = url;
    return this;
  }

  /**
   * Get or create an OpenAI client for the custom endpoint
   * @param {string} baseURL - The base URL for the API
   * @param {string} apiKey - Optional API key (some endpoints may not require it)
   * @returns {OpenAI} - OpenAI client instance
   */
  getClient(baseURL, apiKey = 'dummy-key') {
    const url = baseURL || this.defaultEndpoint;
    const key = `custom-${url}`;

    if (!clients.has(key)) {
      clients.set(key, new OpenAI({
        apiKey: apiKey, // Some APIs may not require authentication
        baseURL: url,
        timeout: 60000
      }));
    }

    return clients.get(key);
  }

  /**
   * Create a chat completion using the custom LLM API
   * @param {Object} options - Chat completion options
   * @param {string} baseURL - Optional custom base URL
   * @returns {Promise<Object>} - Chat completion response
   */
  async createChatCompletion(options, baseURL) {
    const url = baseURL || this.defaultEndpoint;

    try {
      // First try with OpenAI SDK
      try {
        const client = this.getClient(url);
        return await client.chat.completions.create(options);
      } catch (sdkError) {
        console.log('OpenAI SDK failed, falling back to axios:', sdkError.message);

        // Fallback to direct axios call
        const response = await axios({
          method: 'post',
          url: `${url}/chat/completions`,
          headers: {
            'Content-Type': 'application/json'
          },
          data: options,
          timeout: 60000
        });

        return response.data;
      }
    } catch (error) {
      console.error('Error in custom LLM service:', error.message || error);
      throw error;
    }
  }

  /**
   * Convenience method to send a message to the custom LLM API
   * @param {string} message - The message to send
   * @param {string} model - The model to use
   * @param {Object} options - Additional options
   * @param {string} baseURL - Optional custom base URL
   * @returns {Promise<string>} - The response text
   */
  async sendMessage(message, model = 'meta-llama/Llama-3.3-70B-Instruct', options = {}, baseURL) {
    const defaultOptions = {
      model: model,
      messages: [
        { role: 'user', content: message }
      ],
      max_tokens: 512,
      temperature: 0.7,
      top_p: 0.9
    };

    const mergedOptions = { ...defaultOptions, ...options };

    const response = await this.createChatCompletion(mergedOptions, baseURL);
    return response.choices[0].message.content;
  }
}

module.exports = new CustomLLMService();
