const mongoose = require('mongoose');
const Chat = require('../models/chat');
const Conversation = require('../models/conversation');
const OpenAIService = require('./openaiService');
const translationService = require('./translationService');

class ChatService {
  async getConversationContext(conversationId) {
    const chats = await Chat.find(
      { conversation: conversationId },
      'question summaryQuestion answer summaryAnswer createdAt'
    )
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec();

    return chats.reverse().flatMap(chat => [
      { role: 'user', content: chat.summaryQuestion || chat.question },
      { role: 'assistant', content: chat.summaryAnswer || chat.answer }
    ]);
  }

  async saveChatEntry(entryData) {
    const chat = new Chat({
      ...entryData,
      summaryQuestion: await this.summarizeContent(entryData.modelUsed, entryData.question),
      summaryAnswer: await this.summarizeContent(entryData.modelUsed, entryData.answer)
    });

    await chat.save();
    await this.updateConversationTimestamp(entryData.conversation);
  }

  async updateConversationTimestamp(conversationId) {
    await Conversation.findByIdAndUpdate(
      conversationId,
      { updatedAt: Date.now() },
      { new: true }
    );
  }

  async summarizeContent(model, content) {
    if (content.length <= 200) return content;

    try {
      const client = OpenAIService.getClient(model.provider, model);
      const completion = await client.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: 'Hãy tóm tắt nội dung sau ngắn gọn, súc tích, giữ nguyên ý chính:'
          },
          { role: 'user', content }
        ],
        model: model.name,
        temperature: 0.2
      });

      return completion.choices[0].message.content.trim();
    } catch (error) {
      console.error('Summarization failed:', error);
      return content;
    }
  }
}

module.exports = new ChatService();