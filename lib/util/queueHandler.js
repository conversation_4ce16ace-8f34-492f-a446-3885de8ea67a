class QueueHandler {
  constructor() {
    this.queue = [];
    this.processing = false;
  }

  async enqueue(task) {
    return new Promise((resolve) => {
      this.queue.push({ task, resolve });
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0) {
      const { task, resolve } = this.queue.shift();
      try {
        await task();
      } catch (error) {
        console.error('Queue task error:', error);
      }
      resolve();
    }

    this.processing = false;
  }
}

module.exports = QueueHandler;