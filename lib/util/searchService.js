const axios = require('axios');

async function performCustomSearch(query, num_results = 10) {
  try {
    const response = await axios.get('http://*************:5000/search', {
      params: { question: query, num_results }
    });

    return {
      source: response.data.source,
      results: response.data.results.map(item => ({
        title: item.title,
        url: item.url,
        snippet: item.snippet
      }))
    };
  } catch (error) {
    console.error('Search failed:', error);
    return { source: 'error', results: [] };
  }
}

module.exports = { performCustomSearch };