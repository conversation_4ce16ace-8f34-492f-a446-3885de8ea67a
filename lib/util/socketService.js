class SocketService {
  constructor(io) {
    this.io = io;
    this.activeConnections = new Map();
    this.initialize();
  }

  initialize() {
    this.io.on('connection', (socket) => {
      const deviceId = socket.handshake.query.deviceId;
      this.activeConnections.set(deviceId, socket);

      socket.on('disconnect', () => {
        this.cleanupResources(deviceId);
      });
    });
  }

  emitToDevice(deviceId, event, data) {
    const socket = this.activeConnections.get(deviceId);
    if (socket && socket.connected) {
      socket.emit(event, data);
      return true;
    }
    return false;
  }

  cleanupResources(deviceId) {
    // Thêm logic dọn dẹp tài nguyên tại đây
    this.activeConnections.delete(deviceId);
  }
}

module.exports = SocketService;