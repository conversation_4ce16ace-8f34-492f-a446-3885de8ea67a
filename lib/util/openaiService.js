const { OpenAI } = require('openai');
const config = require('config');
const { performCustomSearch } = require('./searchService');

const clients = new Map();

class OpenAIService {
  getClient(provider, modelConfig) {
    const key = `${provider}-${modelConfig.name}`;

    if (!clients.has(key)) {
      clients.set(key, new OpenAI({
        apiKey: config.apiKey[provider][randomNumber(config.apiKey[provider].length)],
        baseURL: modelConfig.url,
        timeout: 60000
      }));
    }

    return clients.get(key);
  }

  async createChatCompletion(client, options) {
    try {
      const mergedOptions = {
        ...options,
        max_tokens: options.max_completion_tokens,
        top_p: options.top_p,
        tools: options.useTools ? this.getTools() : undefined,
        tool_choice: options.useTools ? 'auto' : undefined
      };

      return await client.chat.completions.create(mergedOptions);
    } catch (error) {
      this.handleError(error);
    }
  }

  getTools() {
    return [{
      type: 'function',
      function: {
        name: 'performCustomSearch',
        description: 'Get internet search results',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query' }
          },
          required: ['query']
        }
      }
    }];
  }

  async handleToolCalls(toolCalls, model) {
    const results = [];
    for (const toolCall of toolCalls) {
      if (toolCall.function.name === 'performCustomSearch') {
        const args = JSON.parse(toolCall.function.arguments);
        const searchResults = await performCustomSearch(args.query);
        results.push({
          tool_call_id: toolCall.id,
          role: 'tool',
          name: toolCall.function.name,
          content: JSON.stringify(searchResults.results)
        });
      }
    }
    return results;
  }

  handleError(error) {
    if (error.code === 'rate_limit_exceeded') {
      throw new Error('RATE_LIMIT_EXCEEDED');
    }
    throw error;
  }
}

function randomNumber(max) {
  return Math.floor(Math.random() * max);
}

module.exports = new OpenAIService();