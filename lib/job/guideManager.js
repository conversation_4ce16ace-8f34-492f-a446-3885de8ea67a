const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const uuid = require('uuid/v4');
const axios = require('axios');
const rp = require('request-promise');

class GuideManager {
  constructor() {

  }

  addPetition(id, content, place) {
    
    const userId = uuid()
    const serverChatbot = config.proxyRequestServer.serverChatBot;
    let response;
    let conversationId;

    const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: userId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            ask();
            next({
              code: 200
            });
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          return next({
            code: 400,
            error: error
          })
        })
  }

  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream`, 
        responseType: 'stream', 
        data: {
          "conversation_id": conversationId,
          text: `Tôi là cán bộ xử lý phản ánh trong chính quyền. tôi có nhận được 1 phản ánh của ngươi dân gửi lên. xin vui lòng hướng dẫn cho tôi cách thức xử lý của phản ánh như cần gửi đến đơn vị nào, phòng ban nào, cũng như hướng giải quyết của các phòng ban, đơn vị để thoả đáng cho người dân. nội dung phản ánh như sau: Địa điểm ${place}. Nội dung:${content}`
        },
        timeout:60000
      });

      const stream = resAxios.data;

      resAxios.data.on('data',(chunk) => {
          response += chunk.toString(); 
          response = response.replace('undefined','')
      });
      stream.on('end', () => {
        updatePetition()
      });
      stream.on('error', (err) => {
      });
    } catch (err) {
    }
  }

    const updatePetition = () => {
      PetitionModel
        .update({
          _id: id
        },{
          guideContent: response
        },() => {
        })
    }

    async.waterfall([
      createConversation
    ], (err, data) => {
    })
  }

}

module.exports = new GuideManager;
