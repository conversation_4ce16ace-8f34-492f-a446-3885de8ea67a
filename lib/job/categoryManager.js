const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const uuid = require('uuid/v4');
const axios = require('axios');
const rp = require('request-promise');

class CategoryManager {
  constructor() {
    this.categories = []
    this.categoryString = ''
    this.categoryStringArr = []
    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms('5m'));
  }

  syncConfig() {
    CategoryModel
      .find({
      })
      .lean()
      .exec((err, results) => {
        if(!err && results.length) {
          this.categories = results;
          this.categoryString = results.map(item => item.name).join(",");
          this.categoryStringArr = results.map(item => item.name)
        }
      })
  }

  

  addPetition(id, content) {
    let category
    let categoryReason
    const userId = uuid()
    const serverChatbot = config.proxyRequestServer.serverChatBot;
    let conversationId;
    let response;

    const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: userId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            ask();
            next({
              code: 200
            });
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          console.log(error);
          return next({
            code: 400,
            error: error
          })
        })
  }

  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream`, 
        responseType: 'stream', 
        data: {
          "conversation_id": conversationId,
          text: `Tôi là cán bộ xử lý phản ánh kiến nghị của người dân với nội dung như sau: ${content}. Giúp tôi phân loại phản ánh đó vào một trong các lĩnh vực sau: ${this.categoryString}. Đưa ra lý do tại sao lại chọn`
        },
        timeout:60000
      });

      const stream = resAxios.data;

      resAxios.data.on('data',(chunk) => {

          response += chunk.toString(); 
          response = response.replace('undefined','')

      });
      stream.on('end', () => {
        const categoryText = findFirstMatch(response,this.categoryStringArr)

        this.categories.some((cat) =>{

          if(categoryText == cat.name || categoryText == cat.name.toLowerCase()) {
            category = cat._id;
            categoryReason = response;
            updatePetition()
            return true
          }
        })
        
      });
      stream.on('error', (err) => {
        console.log("ahihi",err)
      });
    } catch (err) {
      console.log("ahihi",err)
    }
  }


  function findFirstMatch(paragraph, fields) {
    let firstField = null;
    let firstIndex = Infinity;
  
    for (const field of fields) {
      const position = paragraph.indexOf(field);
      if (position !== -1) {
        if (position < firstIndex) {
          firstIndex = position;
          firstField = field;
        }
      }
    }
  
    return firstField; // Trả về lĩnh vực xuất hiện đầu tiên
  }

    const updatePetition = () => {
      PetitionModel
        .update({
          _id: id
        },{
          category,
          categoryReason
        },() => {
        })
    }

    async.waterfall([
      createConversation
    ], (err, data) => {
    })
  }

}

module.exports = new CategoryManager;
