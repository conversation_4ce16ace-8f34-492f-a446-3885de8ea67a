const _ = require('lodash')
const async = require('async')
const config = require('config')
const Groq = require('groq-sdk')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Model = require('../../../models/model')


module.exports = (req, res) => {
  const groq = new Groq({ apiKey: config.apiKey.groq[0] });
  let models = [];

  const listModels = (next) => {
    groq.models.list()
      .then(results => {
        if (!results || !results.data || !results.data.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }

        models = results.data
        console.log(models);

        next();
      })
      .catch(err => {
        next(err);
      })
  }

  const insertDB = (next) => {
    async.each(models, (model, callback) => {
      const objUpdate = {
        name: model.id,
        url: 'https://api.groq.com/openai/v1',
        status: model.active ? 1 : 0,
        provider: 'groq',
        ownedBy: model.owned_by.toLowerCase(),
        contextWindow: model.context_window,
        updatedAt: Date.now()
      }
      const options = {
        'new': true,
        upsert: true,
        setDefaultsOnInsert: true
      };

      Model
        .findOneAndUpdate({
          name: model.id
        }, objUpdate, options)
        .lean()
        .exec((err, result) => {
          callback(err);
        })
    }, (err) => {
      if (err) {
        return next(err);
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: models
      });
    });
  }

  async.waterfall([
    listModels,
    insertDB
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
