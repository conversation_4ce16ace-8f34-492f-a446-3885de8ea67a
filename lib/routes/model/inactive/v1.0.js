const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Model = require('../../../models/model')


module.exports = (req, res) => {
  const _id = _.get(req, 'body._id', '');
  const status = _.get(req, 'body.status', 0);

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const create = (next) => {
    Model
      .update({_id}, {status, updatedAt: Date.now()})
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next({
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    create
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
