const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Model = require('../../../models/model')


module.exports = (req, res) => {
  const _id = _.get(req, 'body._id', '');
  const name = _.get(req, 'body.name', '');
  const url = _.get(req, 'body.url', '');

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const create = (next) => {
    const objUpdate = {
      updatedAt: Date.now()
    };
    if (name) {
      objUpdate.name = name;
    }
    if (url) {
      objUpdate.url = url;
    }

    Model
      .update({_id}, objUpdate)
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next({
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    create
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
