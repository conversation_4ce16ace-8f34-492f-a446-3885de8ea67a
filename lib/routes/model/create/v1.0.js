const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Model = require('../../../models/model')


module.exports = (req, res) => {
  const name = _.get(req, 'body.name', '');
  const url = _.get(req, 'body.url', '');

  const checkParams = (next) => {
    if (!name || !url) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const create = (next) => {
    Model
      .create({ name, url }, (err, result) => {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          data: result._id
        })
      })
  }

  async.waterfall([
    checkParams,
    create
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
