const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Model = require('../../../models/model')

module.exports = (req, res) => {
  const platform = _.get(req.body, 'platform', '');
  const platformArr = ['ios', 'android'];

  const list = (next) => {
    const obj = {
      status: 1
    }
    let model = Model.find(obj, 'name provider');
    if (platformArr.includes(platform)) {
      obj.isUseMobile = 1;
      model = Model.findOne(obj, 'name provider');
    }

    model
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results) {
          return next(new Error('Model not found'))
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      });
  }


  async.waterfall([
    list,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
