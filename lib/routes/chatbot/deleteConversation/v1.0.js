const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Conversation = require('../../../models/conversation')
const Chat = require('../../../models/chat')

module.exports = (req, res) => {
  const _id = req.body._id;
  const deviceId = req.body.deviceId;

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const deleteConversation = (next) => {
    Conversation
      .update({
        _id,
        member: deviceId
      },{
        inactive: true
      },(err,result) => {
        next({
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }


  async.waterfall([
    checkParams,
    deleteConversation
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
