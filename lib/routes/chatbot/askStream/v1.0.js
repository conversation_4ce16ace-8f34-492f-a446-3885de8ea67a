const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')
const axios = require('axios');
// const Groq = require('groq-sdk');
const OpenAI = require('openai');
const { GoogleGenerativeAI } = require("@google/generative-ai"); // Added for Gemini
const translate = require("translate-google");
const { detect } = require("langdetect");

module.exports = async (req, res) => {
  // --- L<PERSON>y các tham số từ request ---
  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const modelId = req.body.model || '';
  const location = req.body.location || '';
  const text = _.get(req, 'body.text', '').trim();
  const max_new_tokens = _.get(req, 'body.max_new_tokens', 1028);
  const temperature = _.get(req, 'body.temperature', 0.5);
  const top_k = _.get(req, 'body.top_k', 100);
  const top_p = _.get(req, 'body.top_p', 0.9);

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let response = '';
  let reasoning = '';
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn';
  let error = null;
  let objModel = {};
  let source = '';
  let resultsSearch = [];
  let startReasoning;
  let endReasoning;
  let timeReasoning;
  let textReasoning;
  const translationCache = new Map();
  let conversation

  // Hàng đợi xử lý các chunk để đảm bảo thứ tự phát
  let translationQueue = [];
  let processingQueue = false;
  let ended = false;

  // --- Định nghĩa system prompt dưới dạng 1 chuỗi được ghép từ mảng ---
  const systemPrompt = [
    "Bạn là một trợ lý thông minh có tên là HeyAI, được phát triển bởi các kỹ sư AI Việt Nam. Hãy luôn trả lời một cách hữu ích nhất có thể, đồng thời giữ an toàn.",
    "Hãy trả lời 100% bằng tiếng Việt, không sử dụng từ hoặc ký tự từ các ngôn ngữ khác. Nếu không có từ tương đương trong tiếng Việt, hãy giải thích thay vì dùng từ nước ngoài.",
    `Hãy trả lời người dùng dựa trên thời gian hiện tại nếu cần thiết: ${new Date()}`,
    'Nếu cần thiết sử dụng bất cứ công cụ nào, bạn có thể sử dụng nó mà không cần xin phép.'
  ].join('\n');

  // --- Các hàm tiện ích ---

  // Hàm chuyển các ký tự không phải tiếng Việt sang tiếng Việt nếu cần
  const translateNonVietnameseSentences = async (text, timeout = 10) => {
    await new Promise(resolve => setTimeout(resolve, timeout));
    // Kiểm tra xem văn bản có chứa ký tự không phải Latin không
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);
    if (!hasNonLatinChars) return text;

    // Nếu văn bản đã được dịch trước đó, trả về kết quả cache
    if (translationCache.has(text)) {
      return translationCache.get(text);
    }

    try {
      const translated = await translate(text, { to: 'vi' });
      const translatedText = ' ' + translated.toLowerCase();
      // Lưu kết quả dịch vào cache
      translationCache.set(text, translatedText);
      return translatedText;
    } catch (error) {
      console.error(`Lỗi dịch: ${text}`, error);
      return text;
    }
  };

  // Hàm thêm một tác vụ vào hàng đợi và kích hoạt xử lý
  const enqueueTranslation = (task) => {
    translationQueue.push(task);
    processTranslationQueue();
  };

  // Hàm xử lý hàng đợi tuần tự
  const processTranslationQueue = async () => {
    if (processingQueue) return;
    processingQueue = true;

    while (translationQueue.length > 0) {
      const task = translationQueue.shift();
      try {
        await task();
      } catch (err) {
        console.error('Lỗi xử lý hàng đợi:', err);
      }
    }

    processingQueue = false;
  };

  // Hàm phát thông điệp tới client qua socket (giả sử biến toàn cục io đã được khởi tạo)
  const emitToDevice = (event, payload) => {
    io.to(deviceId).emit(event, payload);
  };

  // Hàm phát reasoning, bao gồm việc dịch và format chuỗi
  const emitNewReasoning = (chunk) => {
    console.log('haha:reasioning', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      reasoning += translated;
      reasoning = reasoning.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      emitToDevice('newReasoning', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning: 'Đang suy nghĩ...',
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Hàm phát nội dung message mới (phần trả lời)
  const emitNewMessage = (chunk, timeout = 10) => {
    console.log('haha:message', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk, timeout);
      response += translated;
      // Loại bỏ các từ thừa
      response = response.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      console.log('push socket to client:', translated)
      emitToDevice('newMessage', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning,
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Khi kết thúc toàn bộ stream, phát thông điệp kết thúc và lưu vào CSDL
  const emitEndMessage = async () => {
    // Đợi cho đến khi hàng đợi hoàn toàn trống
    console.log('haha:end', translationQueue.length, processingQueue)
    while (translationQueue.length > 0 || processingQueue) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    if (ended) return;
    console.log('haha1.1:emitEndMessage')
    ended = true;
    emitToDevice('endMessage', {
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      reasoning,
      textReasoning,
      answer: response,
      createdAt,
      source,
      resultsSearch,
      model: objModel.name
    });
    createCollection();
  };

  // Lưu lịch sử chat vào database
  const createCollection = async () => {
    const usedModel = objModel.modelUse ? objModel.modelUse : objModel;
    const [summaryQuestion, summaryAnswer] = await Promise.all([
      (async () => {
        let s = text.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })(),
      (async () => {
        let s = response.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })()
    ]);
    Chat.create({
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      summaryQuestion,
      reasoning: reasoning.trim(),
      textReasoning,
      answer: response.trim(),
      summaryAnswer,
      createdAt,
      error,
      source,
      resultsSearch,
      model: objModel._id
    }, (err) => {
      if (err) console.error('Lỗi lưu chat:', err);
    });
  };

  // Thực hiện tìm kiếm tùy chỉnh (custom search)
  // time_option = {
  //   "24h",
  //   "week",
  //   "month",
  //   "year",
  //   "all"
  // }
  const performCustomSearch = async (query, num_results = 10, time_option = 'all') => {
    try {
      const res = await axios.get(`http://27.72.59.13:5000/search`, {
        params: { question: query, num_results, time_option }
      });
      return res.data;
    } catch (error) {
      console.error('Error performing custom search:', error);
      return null;
    }
  };

  // Hàm lấy thông tin thời tiết từ Weather API
  const getWeatherData = async (location, days = 1) => {
    try {
      const weatherApiKey = config.weatherApiKey;
      if (!weatherApiKey) {
        console.error('Weather API key not found in configuration');
        return { error: 'Weather API key not configured' };
      }

      // Map of Vietnamese cities with their exact coordinates
      // Using precise coordinates is the most reliable way to get accurate weather data
      const vietnameseCityMap = {
        // Major cities
        'hà nội': { name: 'Hà Nội', lat: 21.0285, lon: 105.8542 },
        'ha noi': { name: 'Hà Nội', lat: 21.0285, lon: 105.8542 },
        'hanoi': { name: 'Hà Nội', lat: 21.0285, lon: 105.8542 },
        'hồ chí minh': { name: 'Hồ Chí Minh', lat: 10.8231, lon: 106.6297 },
        'ho chi minh': { name: 'Hồ Chí Minh', lat: 10.8231, lon: 106.6297 },
        'sài gòn': { name: 'Hồ Chí Minh', lat: 10.8231, lon: 106.6297 },
        'saigon': { name: 'Hồ Chí Minh', lat: 10.8231, lon: 106.6297 },
        'đà nẵng': { name: 'Đà Nẵng', lat: 16.0544, lon: 108.2022 },
        'da nang': { name: 'Đà Nẵng', lat: 16.0544, lon: 108.2022 },
        'hải phòng': { name: 'Hải Phòng', lat: 20.8449, lon: 106.6881 },
        'hai phong': { name: 'Hải Phòng', lat: 20.8449, lon: 106.6881 },
        'cần thơ': { name: 'Cần Thơ', lat: 10.0452, lon: 105.7469 },
        'can tho': { name: 'Cần Thơ', lat: 10.0452, lon: 105.7469 },
        'huế': { name: 'Huế', lat: 16.4637, lon: 107.5909 },
        'hue': { name: 'Huế', lat: 16.4637, lon: 107.5909 },
        'nha trang': { name: 'Nha Trang', lat: 12.2388, lon: 109.1969 },

        // Additional cities
        'bắc ninh': { name: 'Bắc Ninh', lat: 21.1861, lon: 106.0763 },
        'bac ninh': { name: 'Bắc Ninh', lat: 21.1861, lon: 106.0763 },
        'hạ long': { name: 'Hạ Long', lat: 20.9515, lon: 107.0948 },
        'ha long': { name: 'Hạ Long', lat: 20.9515, lon: 107.0948 },
        'vũng tàu': { name: 'Vũng Tàu', lat: 10.3461, lon: 107.0834 },
        'vung tau': { name: 'Vũng Tàu', lat: 10.3461, lon: 107.0834 },
        'buôn ma thuột': { name: 'Buôn Ma Thuột', lat: 12.6675, lon: 108.0377 },
        'buon ma thuot': { name: 'Buôn Ma Thuột', lat: 12.6675, lon: 108.0377 },
        'quy nhơn': { name: 'Quy Nhơn', lat: 13.7748, lon: 109.2243 },
        'quy nhon': { name: 'Quy Nhơn', lat: 13.7748, lon: 109.2243 },
        'vinh': { name: 'Vinh', lat: 18.6734, lon: 105.6922 },
        'thanh hóa': { name: 'Thanh Hóa', lat: 19.8066, lon: 105.7852 },
        'thanh hoa': { name: 'Thanh Hóa', lat: 19.8066, lon: 105.7852 },
        'biên hòa': { name: 'Biên Hòa', lat: 10.9447, lon: 106.8245 },
        'bien hoa': { name: 'Biên Hòa', lat: 10.9447, lon: 106.8245 },
        'thái nguyên': { name: 'Thái Nguyên', lat: 21.5942, lon: 105.8482 },
        'thai nguyen': { name: 'Thái Nguyên', lat: 21.5942, lon: 105.8482 },
        'mỹ tho': { name: 'Mỹ Tho', lat: 10.3543, lon: 106.3639 },
        'my tho': { name: 'Mỹ Tho', lat: 10.3543, lon: 106.3639 },
        'long xuyên': { name: 'Long Xuyên', lat: 10.3864, lon: 105.4355 },
        'long xuyen': { name: 'Long Xuyên', lat: 10.3864, lon: 105.4355 },
        'thủ dầu một': { name: 'Thủ Dầu Một', lat: 10.9804, lon: 106.6519 },
        'thu dau mot': { name: 'Thủ Dầu Một', lat: 10.9804, lon: 106.6519 },
        'hà giang': { name: 'Hà Giang', lat: 22.8233, lon: 104.9884 },
        'ha giang': { name: 'Hà Giang', lat: 22.8233, lon: 104.9884 },
        'cao bằng': { name: 'Cao Bằng', lat: 22.6666, lon: 106.2639 },
        'cao bang': { name: 'Cao Bằng', lat: 22.6666, lon: 106.2639 },
        'lạng sơn': { name: 'Lạng Sơn', lat: 21.8530, lon: 106.7624 },
        'lang son': { name: 'Lạng Sơn', lat: 21.8530, lon: 106.7624 },
        'tuyên quang': { name: 'Tuyên Quang', lat: 21.8232, lon: 105.2135 },
        'tuyen quang': { name: 'Tuyên Quang', lat: 21.8232, lon: 105.2135 },
        'lào cai': { name: 'Lào Cai', lat: 22.4856, lon: 103.9723 },
        'lao cai': { name: 'Lào Cai', lat: 22.4856, lon: 103.9723 },
        'yên bái': { name: 'Yên Bái', lat: 21.7226, lon: 104.9112 },
        'yen bai': { name: 'Yên Bái', lat: 21.7226, lon: 104.9112 },
        'điện biên phủ': { name: 'Điện Biên Phủ', lat: 21.3856, lon: 103.0320 },
        'dien bien phu': { name: 'Điện Biên Phủ', lat: 21.3856, lon: 103.0320 },
        'sơn la': { name: 'Sơn La', lat: 21.3256, lon: 103.9191 },
        'son la': { name: 'Sơn La', lat: 21.3256, lon: 103.9191 },
        'hòa bình': { name: 'Hòa Bình', lat: 20.8133, lon: 105.3383 },
        'hoa binh': { name: 'Hòa Bình', lat: 20.8133, lon: 105.3383 },
        'phú thọ': { name: 'Phú Thọ', lat: 21.3989, lon: 105.2274 },
        'phu tho': { name: 'Phú Thọ', lat: 21.3989, lon: 105.2274 },
        'vĩnh phúc': { name: 'Vĩnh Phúc', lat: 21.3608, lon: 105.5474 },
        'vinh phuc': { name: 'Vĩnh Phúc', lat: 21.3608, lon: 105.5474 },
        'bắc giang': { name: 'Bắc Giang', lat: 21.2731, lon: 106.1946 },
        'bac giang': { name: 'Bắc Giang', lat: 21.2731, lon: 106.1946 },
        'bắc kạn': { name: 'Bắc Kạn', lat: 22.1477, lon: 105.8347 },
        'bac kan': { name: 'Bắc Kạn', lat: 22.1477, lon: 105.8347 },
        'thái bình': { name: 'Thái Bình', lat: 20.4500, lon: 106.3333 },
        'thai binh': { name: 'Thái Bình', lat: 20.4500, lon: 106.3333 },
        'nam định': { name: 'Nam Định', lat: 20.4200, lon: 106.1683 },
        'nam dinh': { name: 'Nam Định', lat: 20.4200, lon: 106.1683 },
        'ninh bình': { name: 'Ninh Bình', lat: 20.2544, lon: 105.9750 },
        'ninh binh': { name: 'Ninh Bình', lat: 20.2544, lon: 105.9750 },
        'hà nam': { name: 'Hà Nam', lat: 20.5464, lon: 105.9131 },
        'ha nam': { name: 'Hà Nam', lat: 20.5464, lon: 105.9131 },
        'hưng yên': { name: 'Hưng Yên', lat: 20.6464, lon: 106.0511 },
        'hung yen': { name: 'Hưng Yên', lat: 20.6464, lon: 106.0511 },
        'hải dương': { name: 'Hải Dương', lat: 20.9372, lon: 106.3145 },
        'hai duong': { name: 'Hải Dương', lat: 20.9372, lon: 106.3145 },
        'quảng ninh': { name: 'Quảng Ninh', lat: 21.0064, lon: 107.2925 },
        'quang ninh': { name: 'Quảng Ninh', lat: 21.0064, lon: 107.2925 },
        'quảng bình': { name: 'Quảng Bình', lat: 17.4688, lon: 106.6222 },
        'quang binh': { name: 'Quảng Bình', lat: 17.4688, lon: 106.6222 },
        'quảng trị': { name: 'Quảng Trị', lat: 16.7943, lon: 107.1000 },
        'quang tri': { name: 'Quảng Trị', lat: 16.7943, lon: 107.1000 },
        'quảng nam': { name: 'Quảng Nam', lat: 15.5394, lon: 108.0191 },
        'quang nam': { name: 'Quảng Nam', lat: 15.5394, lon: 108.0191 },
        'quảng ngãi': { name: 'Quảng Ngãi', lat: 15.1213, lon: 108.7921 },
        'quang ngai': { name: 'Quảng Ngãi', lat: 15.1213, lon: 108.7921 },
        'bình định': { name: 'Bình Định', lat: 13.7827, lon: 109.2239 },
        'binh dinh': { name: 'Bình Định', lat: 13.7827, lon: 109.2239 },
        'phú yên': { name: 'Phú Yên', lat: 13.0881, lon: 109.0928 },
        'phu yen': { name: 'Phú Yên', lat: 13.0881, lon: 109.0928 },
        'khánh hòa': { name: 'Khánh Hòa', lat: 12.2585, lon: 109.0526 },
        'khanh hoa': { name: 'Khánh Hòa', lat: 12.2585, lon: 109.0526 },
        'ninh thuận': { name: 'Ninh Thuận', lat: 11.5603, lon: 108.9903 },
        'ninh thuan': { name: 'Ninh Thuận', lat: 11.5603, lon: 108.9903 },
        'bình thuận': { name: 'Bình Thuận', lat: 10.9434, lon: 108.1000 },
        'binh thuan': { name: 'Bình Thuận', lat: 10.9434, lon: 108.1000 },
        'kon tum': { name: 'Kon Tum', lat: 14.3544, lon: 108.0076 },
        'gia lai': { name: 'Gia Lai', lat: 13.9808, lon: 108.0156 },
        'đắk lắk': { name: 'Đắk Lắk', lat: 12.7100, lon: 108.2377 },
        'dak lak': { name: 'Đắk Lắk', lat: 12.7100, lon: 108.2377 },
        'đắk nông': { name: 'Đắk Nông', lat: 12.0045, lon: 107.6870 },
        'dak nong': { name: 'Đắk Nông', lat: 12.0045, lon: 107.6870 },
        'lâm đồng': { name: 'Lâm Đồng', lat: 11.9404, lon: 108.4583 },
        'lam dong': { name: 'Lâm Đồng', lat: 11.9404, lon: 108.4583 },
        'đà lạt': { name: 'Đà Lạt', lat: 11.9404, lon: 108.4583 },
        'da lat': { name: 'Đà Lạt', lat: 11.9404, lon: 108.4583 },
        'bình phước': { name: 'Bình Phước', lat: 11.7511, lon: 106.7235 },
        'binh phuoc': { name: 'Bình Phước', lat: 11.7511, lon: 106.7235 },
        'tây ninh': { name: 'Tây Ninh', lat: 11.3099, lon: 106.0986 },
        'tay ninh': { name: 'Tây Ninh', lat: 11.3099, lon: 106.0986 },
        'bình dương': { name: 'Bình Dương', lat: 11.1757, lon: 106.6297 },
        'binh duong': { name: 'Bình Dương', lat: 11.1757, lon: 106.6297 },
        'đồng nai': { name: 'Đồng Nai', lat: 10.9380, lon: 106.8230 },
        'dong nai': { name: 'Đồng Nai', lat: 10.9380, lon: 106.8230 },
        'bà rịa - vũng tàu': { name: 'Bà Rịa - Vũng Tàu', lat: 10.5417, lon: 107.2430 },
        'ba ria - vung tau': { name: 'Bà Rịa - Vũng Tàu', lat: 10.5417, lon: 107.2430 },
        'long an': { name: 'Long An', lat: 10.6071, lon: 106.3241 },
        'đồng tháp': { name: 'Đồng Tháp', lat: 10.4938, lon: 105.6886 },
        'dong thap': { name: 'Đồng Tháp', lat: 10.4938, lon: 105.6886 },
        'tiền giang': { name: 'Tiền Giang', lat: 10.3493, lon: 106.3404 },
        'tien giang': { name: 'Tiền Giang', lat: 10.3493, lon: 106.3404 },
        'bến tre': { name: 'Bến Tre', lat: 10.2433, lon: 106.3756 },
        'ben tre': { name: 'Bến Tre', lat: 10.2433, lon: 106.3756 },
        'vĩnh long': { name: 'Vĩnh Long', lat: 10.2537, lon: 105.9722 },
        'vinh long': { name: 'Vĩnh Long', lat: 10.2537, lon: 105.9722 },
        'trà vinh': { name: 'Trà Vinh', lat: 9.9513, lon: 106.3346 },
        'tra vinh': { name: 'Trà Vinh', lat: 9.9513, lon: 106.3346 },
        'hậu giang': { name: 'Hậu Giang', lat: 9.7579, lon: 105.6413 },
        'hau giang': { name: 'Hậu Giang', lat: 9.7579, lon: 105.6413 },
        'kiên giang': { name: 'Kiên Giang', lat: 10.0211, lon: 105.0809 },
        'kien giang': { name: 'Kiên Giang', lat: 10.0211, lon: 105.0809 },
        'an giang': { name: 'An Giang', lat: 10.5215, lon: 105.1258 },
        'bạc liêu': { name: 'Bạc Liêu', lat: 9.2940, lon: 105.7244 },
        'bac lieu': { name: 'Bạc Liêu', lat: 9.2940, lon: 105.7244 },
        'cà mau': { name: 'Cà Mau', lat: 9.1769, lon: 105.1524 },
        'ca mau': { name: 'Cà Mau', lat: 9.1769, lon: 105.1524 },
        'sóc trăng': { name: 'Sóc Trăng', lat: 9.6037, lon: 105.9739 },
        'soc trang': { name: 'Sóc Trăng', lat: 9.6037, lon: 105.9739 }
      };

      // Normalize the location string for comparison
      const normalizedLocation = location.toLowerCase().trim();

      // Check if the location matches any known Vietnamese city
      let queryLocation = location;
      let isVietnameseCity = false;
      let cityInfo = null;

      // Check if the location contains any of our known Vietnamese cities
      for (const [cityKey, info] of Object.entries(vietnameseCityMap)) {
        if (normalizedLocation.includes(cityKey)) {
          isVietnameseCity = true;
          cityInfo = info;
          break;
        }
      }

      // If it's a Vietnamese city, use coordinates for precise location
      if (isVietnameseCity && cityInfo) {
        queryLocation = `${cityInfo.lat},${cityInfo.lon}`; // Use exact coordinates
      } else if (!location.includes(',')) {
        // For other locations, still add Vietnam if it seems to be a Vietnamese query
        if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(location)) {
          queryLocation = `${location}, Vietnam`;
        }
      }

      console.log('haha:location', location, queryLocation);

      // Call the Weather API
      const response = await axios.get(`http://api.weatherapi.com/v1/forecast.json`, {
        params: {
          key: weatherApiKey,
          q: queryLocation, // Use the modified location with coordinates or country
          days: days,
          aqi: 'yes', // Include air quality data
          lang: 'vi'  // Vietnamese language
        }
      });

      // For Vietnamese cities, override the returned location name with our Vietnamese name
      if (isVietnameseCity && cityInfo) {
        // Override the location name with our Vietnamese name for consistency
        response.data.location.name = cityInfo.name;
      }

      console.log('haha:weatherData', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching weather data:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error?.message || 'Không thể lấy dữ liệu thời tiết'
      };
    }
  };

  // Hàm tạo số ngẫu nhiên (dùng để lấy api key)
  const randomNumber = (max) => Math.floor(Math.random() * max);

  // Hàm tóm tắt nội dung khi chuỗi quá dài
  const summarizeContext = async (model, message) => {
    console.log('haha:summarizeContext', model);
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const apiKey = config.apiKey[model.provider][indexKey];

    const systemContent = 'Tóm tắt văn bản dưới đây một cách ngắn gọn, súc tích, giữ đủ ý chính và không cung cấp thêm bất kỳ thông tin hay lời giải thích nào khác:';

    try {
      if (model.provider === 'google') {
        // --- Gemini Summarization ---
        const genAI = new GoogleGenerativeAI(apiKey);
        const geminiModel = genAI.getGenerativeModel({
          model: model.name,
          // Apply system instruction if supported
          ...(model.supportsSystemInstruction ? { systemInstruction: { parts: [{ text: systemContent }] } } : {})
        });

        // Construct prompt for Gemini (system instruction handled above if supported)
        const prompt = !model.supportsSystemInstruction ? `${systemContent}\n\n${message}` : message;

        const result = await geminiModel.generateContent(prompt);
        const response = await result.response;
        const summary = response.text().trim();
        return summary || message; // Return summary or original message if empty

      } else {
        // --- OpenAI Compatible Summarization (Groq, OpenRouter, etc.) ---
        const openai = new OpenAI({
          apiKey: apiKey,
          baseURL: model.url // Use model's specific URL
        });

        const objCreate = {
          messages: [
            { role: 'system', content: systemContent },
            { role: 'user', content: message }
          ],
          model: model.name,
          temperature: temperature || 1, // Consider separate temp for summarization?
          max_tokens: 700,
          top_p: top_p || 1,
          stream: false,
          stop: null,
        };

        // Add OpenRouter headers if needed
        if (model.provider === 'openrouter') {
          objCreate.httpReferer = config.openRouterReferer || 'http://localhost';
          objCreate.siteUrl = config.openRouterSiteUrl || 'http://localhost';
        }

        const chatCompletion = await openai.chat.completions.create(objCreate);
        const summary = chatCompletion.choices[0]?.message?.content?.trim();
        return summary || message; // Return summary or original message if empty
      }
    } catch (err) {
      console.error(`Tóm tắt thất bại với provider ${model.provider}:`, err);
      return message; // Fallback to original message on error
    }
  };

  // --- Các hàm gọi OpenAI / Chatbot ---

  // Phiên bản gọi API truyền thống (không dùng tools)
  const ask = async () => {
    console.log('haha1.1:ask', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/query`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        source = 'LLM';
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr, 0);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản gọi API của Việt Anh
  const askVietAnh = async () => {
    console.log('haha1.1:askVietAnh', Date.now(), promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream_public`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        if (!response) {
          console.log('haha:time', Date.now())
        }
        console.log('haha:data', chunk.toString())
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        console.log('haha:end')
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản dùng OpenAI Compatible APIs (Groq, OpenRouter, etc.) với khả năng sử dụng tools
  const askOpenAICompatible = async (model, useTools = false, toolChoice = 'auto') => {
    if (typeof toolChoice === 'object') {
      console.log('Đang tìm kiếm thêm thông tin...1')
      emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
    }
    const hasSystemRole = promptContext.some(message => message.role === 'system');
    if (!hasSystemRole) {
      promptContext.unshift({ role: 'system', content: systemPrompt });
    }
    const hasToolRole = promptContext.some(message => message.role === 'tool');
    if (hasToolRole) {
      console.log('Đang tổng hợp thông tin...')
      emitToDevice('searching', { text: 'Đang tổng hợp thông tin...' });
    }
    console.log('haha1.1:askOpenAICompatible', model, promptContext) // Updated log
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey] || 'heyu-key',
      baseURL: model.url
    });

    const tools = [
      {
        type: 'function',
        function: {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'getWeatherData',
          description: 'Get weather information for a specific location',
          parameters: {
            type: 'object',
            properties: {
              location: {
                type: 'string',
                description: 'The location to get weather data for (city name, region, lat/long coordinates, etc.)'
              },
              days: {
                type: 'integer',
                description: 'Number of days of forecast (1-7)',
                default: 1
              }
            },
            required: ['location']
          }
        }
      }
    ];

    // --- Adjustments for OpenRouter specific parameters ---
    const objCreate = {
      messages: promptContext,
      model: model.name, // Use model name directly
      temperature: temperature || 1,
      max_tokens: model.maxToken || 512, // OpenRouter might use max_tokens instead of max_completion_tokens
      top_p: top_p || 1,
      stream: true,
      stop: null,
      // OpenRouter specific parameters might go here, e.g., route preference
      // service_tier: 'flex', // Remove Groq specific parameter
    };
    // Add siteUrl and httpReferer for OpenRouter identification (optional but recommended)
    if (model.provider === 'openrouter') {
      objCreate.httpReferer = config.openRouterReferer || 'http://localhost'; // Get from config or default
      objCreate.siteUrl = config.openRouterSiteUrl || 'http://localhost'; // Get from config or default
    }
    // --- End OpenRouter specific adjustments ---

    // Reasoning might not be supported by all OpenRouter models or needs different handling
    if (model.reasoning && model.provider !== 'openrouter') {
      objCreate.reasoning_format = model.reasoning;
    }
    if (useTools) {
      objCreate.tools = tools;
      objCreate.tool_choice = toolChoice;
    }

    try {
      const chatCompletion = await openai.chat.completions.create(objCreate);
      let toolCalls = []; // Use an array to store potentially multiple tool calls
      let currentToolCallFragments = {}; // Store fragments for each tool call index

      if (chatCompletion && typeof chatCompletion[Symbol.asyncIterator] === 'function') {
        for await (const chunk of chatCompletion) {
          // --- Tool Call Handling (Combine fragmented tool calls) ---
          if (chunk?.choices[0]?.delta?.tool_calls?.length) {
            chunk.choices[0].delta.tool_calls.forEach(deltaToolCall => {
              if (deltaToolCall.index == null) return; // Skip if no index

              if (!currentToolCallFragments[deltaToolCall.index]) {
                // First fragment for this call index
                currentToolCallFragments[deltaToolCall.index] = { ...deltaToolCall };
              } else {
                // Subsequent fragments: merge properties
                const currentCall = currentToolCallFragments[deltaToolCall.index];
                if (deltaToolCall.id) currentCall.id = deltaToolCall.id;
                if (deltaToolCall.type) currentCall.type = deltaToolCall.type;
                if (deltaToolCall.function) {
                  if (!currentCall.function) currentCall.function = {};
                  if (deltaToolCall.function.name) currentCall.function.name = deltaToolCall.function.name;
                  if (deltaToolCall.function.arguments) {
                    currentCall.function.arguments = (currentCall.function.arguments || "") + deltaToolCall.function.arguments;
                  }
                }
              }
            });
            console.log('Đang tìm kiếm thêm thông tin...')
            emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
          }
          // --- End Tool Call Handling ---

          if (chunk?.choices[0]?.delta?.reasoning) {
            if (!reasoning) {
              startReasoning = Date.now();
            }
            emitNewReasoning(chunk.choices[0].delta.reasoning);
          }
          if (chunk?.choices[0]?.delta?.content) {
            if (!response && startReasoning) {
              endReasoning = Date.now();
              timeReasoning = endReasoning - startReasoning;
              textReasoning = `Suy nghĩ trong ${Math.floor(timeReasoning / 1000) || 1} giây`
            }
            emitNewMessage(chunk.choices[0].delta.content);
          }
        }

        // --- Process completed tool calls after stream ends ---
        toolCalls = Object.values(currentToolCallFragments); // Convert fragments object to array
        if (toolCalls.length > 0) {
          const toolResults = [];
          for (const toolCall of toolCalls) {
            // Ensure arguments are valid JSON before parsing
            let args;
            try {
              // Clean up the arguments string to ensure it's valid JSON
              let argsStr = toolCall.function.arguments || '{}';

              // Check if there are multiple JSON objects concatenated (a common issue)
              if (argsStr.includes('}{')) {
                console.log('Detected multiple concatenated JSON objects, fixing...');
                // Take only the first valid JSON object
                argsStr = argsStr.substring(0, argsStr.indexOf('}')+1);
              }

              // Additional cleanup for any other potential JSON issues
              argsStr = argsStr.trim();

              console.log('Cleaned arguments string:', argsStr);
              args = JSON.parse(argsStr);
            } catch (parseError) {
              console.error('Failed to parse tool call arguments:', toolCall.function.arguments, parseError);
              // Handle error: maybe skip this tool call or send an error message
              continue; // Skip this tool call
            }

            if (toolCall.function.name === 'performCustomSearch') {
              console.log('haha:args search', args)

              // Ensure we have a valid query string
              const query = typeof args.query === 'string' ? args.query :
                           (args.query ? String(args.query) : '');

              // Make sure we don't pass an empty query
              if (!query.trim()) {
                console.error('Empty query detected in OpenAI function call');
                // Use the original user query as fallback
                const userQuery = promptContext[promptContext.length - 1]?.content || text;
                var searchResults = await performCustomSearch(userQuery);
              } else {
                var searchResults = await performCustomSearch(query);
              }

              // Only proceed if we have valid search results
              if (searchResults && searchResults.results) {
                toolResults.push({
                  tool_call_id: toolCall.id,
                  role: 'tool',
                  name: toolCall.function.name,
                  content: JSON.stringify(
                    searchResults.results.map(item => {
                      const { content, ...rest } = item;
                      return rest;
                    })
                  )
                });
                source = searchResults.source;
                resultsSearch = searchResults.results;
              } else {
                console.error('No valid search results returned');
                // Add a fallback response
                toolResults.push({
                  tool_call_id: toolCall.id,
                  role: 'tool',
                  name: toolCall.function.name,
                  content: JSON.stringify([{ title: "Không tìm thấy kết quả phù hợp", url: "" }])
                });
              }
            } else if (toolCall.function.name === 'getWeatherData') {
              console.log('Weather API call:', args);

              // Ensure we have a valid location string
              const location = typeof args.location === 'string' ? args.location :
                              (args.location ? String(args.location) : '');

              // Get days parameter with default value of 1
              const days = typeof args.days === 'number' ? Math.min(Math.max(args.days, 1), 7) : 1;

              // Make sure we don't pass an empty location
              if (!location.trim()) {
                console.error('Empty location detected in weather API call');
                toolResults.push({
                  tool_call_id: toolCall.id,
                  role: 'tool',
                  name: toolCall.function.name,
                  content: JSON.stringify({
                    error: "Vui lòng cung cấp tên địa điểm cụ thể để xem thông tin thời tiết"
                  })
                });
              } else {
                // Call the weather API
                const weatherData = await getWeatherData(location, days);

                // Format the response
                console.log('haha:weatherData', weatherData);
                if (weatherData.success) {
                  toolResults.push({
                    tool_call_id: toolCall.id,
                    role: 'tool',
                    name: toolCall.function.name,
                    content: JSON.stringify(weatherData.data)
                  });
                } else {
                  toolResults.push({
                    tool_call_id: toolCall.id,
                    role: 'tool',
                    name: toolCall.function.name,
                    content: JSON.stringify({
                      error: weatherData.error || "Không thể lấy dữ liệu thời tiết cho địa điểm này"
                    })
                  });
                }
              }
            }
            // Add handling for other potential tool names here
          }

          // Khi có tool call, thêm kết quả vào prompt và gọi lại askOpenAICompatible
          if (toolResults.length > 0) {
            promptContext = promptContext.concat(toolResults);
            await askOpenAICompatible(model); // Recursive call without tools initially
          } else {
            emitEndMessage(); // No valid tool results to process
          }
        } else {
          // No tool calls, check if response exists or queue has items
          if (response.trim() || translationQueue.length) {
            emitEndMessage();
          } else {
            // Handle cases where the stream ended without content or tool calls
            console.log('Stream ended without response or tool calls, retrying...');
            await askOpenAICompatible(model); // Retry the call
          }
        }
        // --- End Tool Call Processing ---

      } else {
        console.log('haha:chatCompletion (non-stream or error)', chatCompletion)
        // Handle non-stream response or error case - maybe retry?
        handleError(new Error('Non-stream response or unexpected completion object'));
      }
    } catch (err) {
      // Improved Error Handling
      console.error('askOpenAICompatible Error:', err);
      let userMessage = messageError; // Default error message
      if (err.error?.code === 'tool_use_failed') {
        console.log('haha:tool_use_failed', err)
        // Maybe retry without tools? Or just inform the user.
        userMessage = 'Lỗi khi sử dụng công cụ tìm kiếm. Đang thử lại...';
        // Decide whether to retry automatically or just fail
        // For now, just fail with a specific message
        handleError(err, 'Lỗi khi sử dụng công cụ tìm kiếm. Vui lòng thử lại.');
        // Or retry: askOpenAICompatible(model, false); // Retry without tools
      } else if (err.error?.code === 'rate_limit_exceeded' || err.status === 429) {
        userMessage = 'Bạn đang hỏi quá nhiều trong thời gian này. Bạn vui lòng đợi ít phút rồi thử lại. Xin cảm ơn.';
        handleError(err, userMessage);
      } else if (err.error?.code === 'context_length_exceeded') {
        userMessage = 'Câu hỏi hoặc ngữ cảnh quá dài. Vui lòng rút gọn và thử lại.';
        handleError(err, userMessage);
      } else {
        // Generic error
        // handleError(err);
        const resAxios = await axios({
          method: 'post',
          url: `http://27.72.59.13:8999/v1/chat/completions `,
          responseType: 'stream',
          data: {
            messages: promptContext,
            temperature,
            top_k,
            top_p
          },
          timeout: 60000
        });

        const stream = resAxios.data;
        stream.on('data', (chunk) => {
          const chunkStr = chunk.toString();
          emitNewMessage(chunkStr);
        });
        stream.on('end', () => {
          emitEndMessage();
        });
      }
    }
  };

  // Phiên bản dùng Google Gemini API (with Function Calling)
  const askGemini = async (model) => {
    console.log('haha1.1:askGemini with tools', model, promptContext);
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const apiKey = config.apiKey[model.provider][indexKey];
    const genAI = new GoogleGenerativeAI(apiKey);

    // --- Define Tools for Gemini ---
    const tools = [{
      functionDeclarations: [
        {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'OBJECT', // Gemini uses uppercase OBJECT
            properties: {
              query: {
                type: 'STRING', // Gemini uses uppercase STRING
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        },
        {
          name: 'getWeatherData',
          description: 'Get weather information for a specific location',
          parameters: {
            type: 'OBJECT', // Gemini uses uppercase OBJECT
            properties: {
              location: {
                type: 'STRING', // Gemini uses uppercase STRING
                description: 'The location to get weather data for (city name, region, lat/long coordinates, etc.)'
              },
              days: {
                type: 'INTEGER', // Gemini uses uppercase INTEGER
                description: 'Number of days of forecast (1-7)',
                default: 1
              }
            },
            required: ['location']
          }
        }
      ]
    }];

    // --- Format promptContext for Gemini ---
    const history = promptContext.slice(0, -1).map(msg => {
      // Map OpenAI roles to Gemini roles ('tool' -> 'function')
      const role = msg.role === 'assistant' ? 'model' : (msg.role === 'tool' ? 'function' : 'user');
      // Gemini expects 'parts' to be an array
      let parts = [];
      if (msg.role === 'tool') {
        // Format tool response for Gemini
        parts.push({
          functionResponse: {
            name: msg.name, // Assuming msg.name holds the function name for tool responses
            response: { content: msg.content } // Wrap content in response object
          }
        });
      } else {
        parts.push({ text: msg.content });
      }
      return { role, parts };
    });
    const currentUserQuery = promptContext[promptContext.length - 1].content;

    // --- System Prompt Handling ---
    const systemInstructionIndex = history.findIndex(item => item.role === 'system'); // System role isn't directly used in Gemini history
    let systemInstructionContent = systemPrompt;
    if (systemInstructionIndex !== -1) {
      systemInstructionContent = history[systemInstructionIndex].parts[0].text;
      history.splice(systemInstructionIndex, 1); // Remove from history
    }

    // --- Initialize Model and Chat ---
    const geminiModel = genAI.getGenerativeModel({
      model: model.name,
      // Pass system instruction if supported
      ...(model.supportsSystemInstruction ? { systemInstruction: { parts: [{ text: systemInstructionContent }] } } : {}),
      tools: tools // Provide the defined tools
    });

    const chat = geminiModel.startChat({
      history: history,
      generationConfig: {
        maxOutputTokens: max_new_tokens || 1500,
        temperature: temperature || 0.7,
        topP: top_p || 0.9,
        topK: top_k || 40,
      }
    });

    console.log('Sending initial message to Gemini:', currentUserQuery);

    try {
      let result = await chat.sendMessageStream(currentUserQuery);
      let aggregatedResponse = { text: '', functionCalls: [] }; // Store aggregated response

      // --- Process First Stream (Initial Response or Function Call) ---
      for await (const chunk of result.stream) {
        // Aggregate text and function calls from chunks
        if (chunk.text) {
          aggregatedResponse.text += chunk.text();
        }
        if (chunk.functionCalls) {
          // Ensure functionCalls is always an array and merge
          const calls = chunk.functionCalls(); // Get the array of calls
          if (Array.isArray(calls)) {
            aggregatedResponse.functionCalls.push(...calls);
          }
        }
      }
      console.log('Gemini initial response aggregated:', aggregatedResponse);


      // --- Handle Function Call if Present ---
      if (aggregatedResponse.functionCalls && aggregatedResponse.functionCalls.length > 0) {
        console.log('Đang tìm kiếm thêm thông tin... (Gemini)');
        emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });

        const functionCall = aggregatedResponse.functionCalls[0]; // Process the first call for now
        const functionName = functionCall.name;
        const args = functionCall.args;

        if (functionName === 'performCustomSearch') {
          console.log('haha:args search (Gemini)', args);
          // Ensure we have a valid query string
          const query = typeof args.query === 'string' ? args.query :
                       (args.query ? String(args.query) : '');

          // Make sure we don't pass an empty query
          if (!query.trim()) {
            console.error('Empty query detected in Gemini function call');
            // Use the original user query as fallback
            const userQuery = currentUserQuery || text;
            const searchResults = await performCustomSearch(userQuery);
            source = searchResults.source;
            resultsSearch = searchResults.results;
          } else {
            const searchResults = await performCustomSearch(query);
            source = searchResults.source; // Update global source/results
            resultsSearch = searchResults.results;
          }
        } else if (functionName === 'getWeatherData') {
          console.log('Weather API call (Gemini):', args);

          // Ensure we have a valid location string
          const location = typeof args.location === 'string' ? args.location :
                          (args.location ? String(args.location) : '');

          // Get days parameter with default value of 1
          const days = typeof args.days === 'number' ? Math.min(Math.max(args.days, 1), 7) : 1;

          let weatherData;

          // Make sure we don't pass an empty location
          if (!location.trim()) {
            console.error('Empty location detected in Gemini weather API call');
            weatherData = {
              success: false,
              error: "Vui lòng cung cấp tên địa điểm cụ thể để xem thông tin thời tiết"
            };
          } else {
            // Call the weather API
            weatherData = await getWeatherData(location, days);
            console.log('Gemini weather data:', weatherData);
          }

          // Format response for Gemini function response
          let functionResponseContent;

          if (functionName === 'performCustomSearch') {
            functionResponseContent = JSON.stringify(
              (searchResults && searchResults.results) ?
                searchResults.results.map(item => {
                  const { content, ...rest } = item; // Exclude content field if needed
                  return rest;
                }) :
                [{ title: "Không tìm thấy kết quả phù hợp", url: "" }]
            );
          } else if (functionName === 'getWeatherData') {
            functionResponseContent = JSON.stringify(
              weatherData.success ? weatherData.data :
              { error: weatherData.error || "Không thể lấy dữ liệu thời tiết cho địa điểm này" }
            );
          }

          // --- Send Function Response Back to Gemini ---
          console.log('Sending function response back to Gemini');
          result = await chat.sendMessageStream([ // Send as array of parts
            {
              functionResponse: {
                name: functionName,
                response: { content: functionResponseContent }, // Wrap content
              }
            }
          ]);

          // --- Process Second Stream (Final Answer After Function Call) ---
          console.log('Processing Gemini stream after function response');
          for await (const chunk of result.stream) {
            try {
              const chunkText = chunk.text();
              if (chunkText) {
                emitNewMessage(chunkText);
              }
            } catch (streamError) {
              console.error("Error processing Gemini stream chunk after function call:", streamError);
            }
          }
        } else {
          console.warn(`Unsupported function call requested by Gemini: ${functionName}`);
          // Handle unsupported function call - maybe send an error message back?
          // For now, just proceed without calling anything.
        }
      } else {
        // --- No Function Call - Stream the Initial Text Response ---
        if (aggregatedResponse.text) {
          // Since we aggregated, emit the full text at once or chunk it if needed
          // For simplicity here, emitting aggregated text. Consider re-chunking if needed.
          emitNewMessage(aggregatedResponse.text);
        }
      }

      // --- Final Check and End Message ---
      if (response.trim() || translationQueue.length > 0) {
        emitEndMessage();
      } else {
        console.log('Gemini stream ended without final response after processing.');
        // Consider retry or specific error handling
        handleError(new Error('Gemini stream ended without final response'));
      }

    } catch (err) {
      console.error('askGemini Error:', err);
      handleError(err);
    }
  };

  const handleError = (err, msg = messageError) => {
    console.log('haha:error', err)
    error = err;
    response = msg;
    emitEndMessage();
  };

  const askRAG = async () => {
    console.log('haha1.1:askRAG', promptContext)
    const serverRAG = objModel.urlRAG || 'http://203.171.31.42:8001'
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverRAG}/query_stream`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        source = 'RAG';
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  const classify = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/classify`,
        data: {
          messages: promptContext
        },
        timeout: 60000
      });

      const { destination } = resAxios.data.category;
      console.log('haha:destination', destination)
      if (destination === 'RAG') {
        askRAG();
      } else if (destination === 'Search') {
        toolChoice = {
          type: 'function',
          function: { name: 'performCustomSearch' }
        };
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true, toolChoice);
      } else {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      }
    } catch (err) {
      console.log('haha:err clas', err)
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  }

  // --- Xây dựng promptContext từ lịch sử hội thoại ---
  let promptContext = [{ role: 'user', content: text }];
  try {
    // Kiểm tra các tham số bắt buộc
    if (!deviceId || !serverChatbot || !conversationId || !modelId) {
      throw new Error(`code: ${CONSTANTS.CODE.WRONG_PARAMS}`);
    }

    // Lấy thông tin model
    if (modelId) {
      const modelDoc = await Model.findOne({ _id: modelId }, 'name url provider modelUse useGroq reasoning urlRAG maxToken')
        .lean()
        .exec();
      if (!modelDoc || !modelDoc.url) throw new Error('Model not found');
      objModel = modelDoc;
      serverChatbot = objModel.url;
    }

    // Kiểm tra sự tồn tại của conversation và cập nhật updatedAt
    const conv = await Conversation.findOneAndUpdate(
      { _id: conversationId },
      { updatedAt: Date.now() }
    )
      .lean()
      .exec();
    if (!conv) throw new Error(`Conversation not found`);
    conversation = conv;

    // Lấy 5 chat gần nhất làm context
    const chats = await Chat.find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec();
    if (chats.length) {
      const context = chats
        .reverse()
        .map(chat => ({
          role: 'user',
          content: chat.summaryQuestion ? chat.summaryQuestion.trim() : chat.question.trim()
        }))
        .flatMap((userMessage, index) => [
          userMessage,
          {
            role: 'assistant',
            content: chats[index].summaryAnswer ? chats[index].summaryAnswer.trim() : chats[index].answer.trim()
          }
        ]);
      promptContext = context.concat(promptContext);
    }

    const listProvider = ['groq', 'openrouter', 'heyai'];

    // Determine which function to call based on provider
    if (objModel.provider === 'vietanh') {
      askVietAnh();
    } else if (objModel.provider === 'google') {
      askGemini(objModel.modelUse ? objModel.modelUse : objModel);
    } else if (listProvider.includes(objModel.provider) || objModel.useGroq) { // Added 'openrouter'
      // Use the OpenAI compatible function for Groq, OpenRouter, or if useGroq is true
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    } else if (objModel.provider === 'heyu' && !objModel.urlRAG) { // Specific check for 'heyu' without RAG
      ask(); // Original 'heyu' ask function
    } else if (objModel.provider === 'heyu' && objModel.urlRAG) { // Specific check for 'heyu' with RAG
      classify(); // Use classify which might lead to askRAG or askOpenAICompatible
    } else {
      // Fallback or handle unknown providers - maybe default to OpenAICompatible or throw error
      console.warn(`Unknown or unhandled provider: ${objModel.provider}. Falling back to OpenAICompatible.`);
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true); // Fallback
    }
    res.json({ code: CONSTANTS.CODE.SUCCESS });
  } catch (err) {
    console.error(err);
    res.json({ code: CONSTANTS.CODE.SYSTEM_ERROR, message: MESSAGES.SYSTEM.ERROR });
  }
};
