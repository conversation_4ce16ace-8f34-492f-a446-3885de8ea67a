const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')
const axios = require('axios');
// const Groq = require('groq-sdk');
const OpenAI = require('openai');
const translate = require("translate-google");
const { detect } = require("langdetect");

module.exports = async (req, res) => {
  // --- <PERSON><PERSON><PERSON> các tham số từ request ---
  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const modelId = req.body.model || '';
  const location = req.body.location || '';
  const text = _.get(req, 'body.text', '').trim();
  const max_new_tokens = _.get(req, 'body.max_new_tokens', 1028);
  const temperature = _.get(req, 'body.temperature', 0.5);
  const top_k = _.get(req, 'body.top_k', 100);
  const top_p = _.get(req, 'body.top_p', 0.9);

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let response = '';
  let reasoning = '';
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn';
  let error = null;
  let objModel = {};
  let source = '';
  let resultsSearch = [];
  let startReasoning;
  let endReasoning;
  let timeReasoning;
  let textReasoning;
  const translationCache = new Map();
  let conversation

  // Hàng đợi xử lý các chunk để đảm bảo thứ tự phát
  let translationQueue = [];
  let processingQueue = false;
  let ended = false;

  // --- Định nghĩa system prompt dưới dạng 1 chuỗi được ghép từ mảng ---
  const systemPrompt = [
    "Bạn là một trợ lý thông minh có tên là HeyAI, được phát triển bởi các kỹ sư AI Việt Nam. Hãy luôn trả lời một cách hữu ích nhất có thể, đồng thời giữ an toàn.",
    "Hãy trả lời 100% bằng tiếng Việt, không sử dụng từ hoặc ký tự từ các ngôn ngữ khác. Nếu không có từ tương đương trong tiếng Việt, hãy giải thích thay vì dùng từ nước ngoài.",
    `Hãy trả lời người dùng dựa trên thời gian hiện tại nếu cần thiết: ${new Date()}`,
    'Nếu cần thiết sử dụng bất cứ công cụ nào, bạn có thể sử dụng nó mà không cần xin phép.'
  ].join('\n');

  // --- Các hàm tiện ích ---

  // Hàm chuyển các ký tự không phải tiếng Việt sang tiếng Việt nếu cần
  const translateNonVietnameseSentences = async (text, timeout = 10) => {
    await new Promise(resolve => setTimeout(resolve, timeout));
    // Kiểm tra xem văn bản có chứa ký tự không phải Latin không
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);
    if (!hasNonLatinChars) return text;

    // Nếu văn bản đã được dịch trước đó, trả về kết quả cache
    if (translationCache.has(text)) {
      return translationCache.get(text);
    }

    try {
      const translated = await translate(text, { to: 'vi' });
      const translatedText = ' ' + translated.toLowerCase();
      // Lưu kết quả dịch vào cache
      translationCache.set(text, translatedText);
      return translatedText;
    } catch (error) {
      console.error(`Lỗi dịch: ${text}`, error);
      return text;
    }
  };

  // Hàm thêm một tác vụ vào hàng đợi và kích hoạt xử lý
  const enqueueTranslation = (task) => {
    translationQueue.push(task);
    processTranslationQueue();
  };

  // Hàm xử lý hàng đợi tuần tự
  const processTranslationQueue = async () => {
    if (processingQueue) return;
    processingQueue = true;

    while (translationQueue.length > 0) {
      const task = translationQueue.shift();
      try {
        await task();
      } catch (err) {
        console.error('Lỗi xử lý hàng đợi:', err);
      }
    }

    processingQueue = false;
  };

  // Hàm phát thông điệp tới client qua socket (giả sử biến toàn cục io đã được khởi tạo)
  const emitToDevice = (event, payload) => {
    io.to(deviceId).emit(event, payload);
  };

  // Hàm phát reasoning, bao gồm việc dịch và format chuỗi
  const emitNewReasoning = (chunk) => {
    console.log('haha:reasioning', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      reasoning += translated;
      reasoning = reasoning.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      emitToDevice('newReasoning', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning: 'Đang suy nghĩ...',
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Hàm phát nội dung message mới (phần trả lời)
  const emitNewMessage = (chunk, timeout = 10) => {
    console.log('haha:message', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk, timeout);
      response += translated;
      // Loại bỏ các từ thừa
      response = response.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      console.log('push socket to client:', translated)
      emitToDevice('newMessage', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning,
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Khi kết thúc toàn bộ stream, phát thông điệp kết thúc và lưu vào CSDL
  const emitEndMessage = async () => {
    // Đợi cho đến khi hàng đợi hoàn toàn trống
    console.log('haha:end', translationQueue.length, processingQueue)
    while (translationQueue.length > 0 || processingQueue) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    if (ended) return;
    console.log('haha1.1:emitEndMessage')
    ended = true;
    emitToDevice('endMessage', {
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      reasoning,
      textReasoning,
      answer: response,
      createdAt,
      source,
      resultsSearch,
      model: objModel.name
    });
    createCollection();
  };

  // Lưu lịch sử chat vào database
  const createCollection = async () => {
    const usedModel = objModel.modelUse ? objModel.modelUse : objModel;
    const [summaryQuestion, summaryAnswer] = await Promise.all([
      (async () => {
        let s = text.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })(),
      (async () => {
        let s = response.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })()
    ]);
    Chat.create({
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      summaryQuestion,
      reasoning: reasoning.trim(),
      textReasoning,
      answer: response.trim(),
      summaryAnswer,
      createdAt,
      error,
      source,
      resultsSearch,
      model: objModel._id
    }, (err) => {
      if (err) console.error('Lỗi lưu chat:', err);
    });
  };

  // Thực hiện tìm kiếm tùy chỉnh (custom search)
  // time_option = {
  //   "24h",
  //   "week",
  //   "month",
  //   "year",
  //   "all"
  // }
  const performCustomSearch = async (query, num_results = 10, time_option = 'all') => {
    try {
      const res = await axios.get(`http://27.72.59.13:5000/search`, {
        params: { question: query, num_results, time_option }
      });
      return res.data;
    } catch (error) {
      console.error('Error performing custom search:', error);
      return null;
    }
  };

  // Hàm tạo số ngẫu nhiên (dùng để lấy api key)
  const randomNumber = (max) => Math.floor(Math.random() * max);

  // Hàm tóm tắt nội dung khi chuỗi quá dài
  const summarizeContext = async (model, message) => {
    console.log('haha:summarizeContext', model)
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url || 'https://api.groq.com/openai/v1'
    });
    try {
      const objCreate = {
        messages: [
          {
            role: 'system',
            content: 'Tóm tắt văn bản dưới đây một cách ngắn gọn, súc tích, giữ đủ ý chính và không cung cấp thêm bất kỳ thông tin hay lời giải thích nào khác:'
          },
          { role: 'user', content: `${message}` }
        ],
        model: model.name, // Use model name directly
        temperature: temperature || 1,
        max_tokens: 700, // Use max_tokens for broader compatibility
        top_p: top_p || 1,
        stream: false,
        stop: null,
        // service_tier: 'flex', // Remove Groq specific
      };
      // Add OpenRouter headers if needed
      if (model.provider === 'openrouter') {
        objCreate.httpReferer = config.openRouterReferer || 'http://localhost'; // Get from config or default
        objCreate.siteUrl = config.openRouterSiteUrl || 'http://localhost'; // Get from config or default
      }
      // Reasoning might not apply here or needs checking for OpenRouter
      // if (model.reasoning && model.provider !== 'openrouter') objCreate.reasoning_format = 'hidden';

      const chatCompletion = await openai.chat.completions.create(objCreate);
      return chatCompletion.choices[0].message.content.trim() || message;
    } catch (err) {
      console.error('Tóm tắt thất bại:', err);
      return message;
    }
  };

  // --- Các hàm gọi OpenAI / Chatbot ---

  // Phiên bản gọi API truyền thống (không dùng tools)
  const ask = async () => {
    console.log('haha1.1:ask', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/query`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        source = 'LLM';
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr, 0);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản gọi API của Việt Anh
  const askVietAnh = async () => {
    console.log('haha1.1:askVietAnh', Date.now(), promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream_public`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        if (!response) {
          console.log('haha:time', Date.now())
        }
        console.log('haha:data', chunk.toString())
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        console.log('haha:end')
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản dùng OpenAI Compatible APIs (Groq, OpenRouter, etc.) với khả năng sử dụng tools
  const askOpenAICompatible = async (model, useTools = false, toolChoice = 'auto') => {
    if (typeof toolChoice === 'object') {
      console.log('Đang tìm kiếm thêm thông tin...1')
      emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
    }
    const hasSystemRole = promptContext.some(message => message.role === 'system');
    if (!hasSystemRole) {
      promptContext.unshift({ role: 'system', content: systemPrompt });
    }
    const hasToolRole = promptContext.some(message => message.role === 'tool');
    if (hasToolRole) {
      console.log('Đang tổng hợp thông tin...')
      emitToDevice('searching', { text: 'Đang tổng hợp thông tin...' });
    }
    console.log('haha1.1:askOpenAICompatible', model, promptContext) // Updated log
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url
    });

    const tools = [
      {
        type: 'function',
        function: {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        }
      }
    ];

    // --- Adjustments for OpenRouter specific parameters ---
    const objCreate = {
      messages: promptContext,
      model: model.name, // Use model name directly
      temperature: temperature || 1,
      max_tokens: 1500, // OpenRouter might use max_tokens instead of max_completion_tokens
      top_p: top_p || 1,
      stream: true,
      stop: null,
      // OpenRouter specific parameters might go here, e.g., route preference
      // service_tier: 'flex', // Remove Groq specific parameter
    };
    // Add siteUrl and httpReferer for OpenRouter identification (optional but recommended)
    if (model.provider === 'openrouter') {
      objCreate.httpReferer = config.openRouterReferer || 'http://localhost'; // Get from config or default
      objCreate.siteUrl = config.openRouterSiteUrl || 'http://localhost'; // Get from config or default
    }
    // --- End OpenRouter specific adjustments ---

    // Reasoning might not be supported by all OpenRouter models or needs different handling
    if (model.reasoning && model.provider !== 'openrouter') {
      objCreate.reasoning_format = model.reasoning;
    }
    if (useTools) {
      objCreate.tools = tools;
      objCreate.tool_choice = toolChoice;
    }

    try {
      const chatCompletion = await openai.chat.completions.create(objCreate);
      let toolCalls = []; // Use an array to store potentially multiple tool calls
      let currentToolCallFragments = {}; // Store fragments for each tool call index

      if (chatCompletion && typeof chatCompletion[Symbol.asyncIterator] === 'function') {
        for await (const chunk of chatCompletion) {
          // --- Tool Call Handling (Combine fragmented tool calls) ---
          if (chunk?.choices[0]?.delta?.tool_calls?.length) {
            chunk.choices[0].delta.tool_calls.forEach(deltaToolCall => {
              if (deltaToolCall.index == null) return; // Skip if no index

              if (!currentToolCallFragments[deltaToolCall.index]) {
                // First fragment for this call index
                currentToolCallFragments[deltaToolCall.index] = { ...deltaToolCall };
              } else {
                // Subsequent fragments: merge properties
                const currentCall = currentToolCallFragments[deltaToolCall.index];
                if (deltaToolCall.id) currentCall.id = deltaToolCall.id;
                if (deltaToolCall.type) currentCall.type = deltaToolCall.type;
                if (deltaToolCall.function) {
                  if (!currentCall.function) currentCall.function = {};
                  if (deltaToolCall.function.name) currentCall.function.name = deltaToolCall.function.name;
                  if (deltaToolCall.function.arguments) {
                    currentCall.function.arguments = (currentCall.function.arguments || "") + deltaToolCall.function.arguments;
                  }
                }
              }
            });
            console.log('Đang tìm kiếm thêm thông tin...')
            emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
          }
          // --- End Tool Call Handling ---

          if (chunk?.choices[0]?.delta?.reasoning) {
            if (!reasoning) {
              startReasoning = Date.now();
            }
            emitNewReasoning(chunk.choices[0].delta.reasoning);
          }
          if (chunk?.choices[0]?.delta?.content) {
            if (!response && startReasoning) {
              endReasoning = Date.now();
              timeReasoning = endReasoning - startReasoning;
              textReasoning = `Suy nghĩ trong ${Math.floor(timeReasoning / 1000) || 1} giây`
            }
            emitNewMessage(chunk.choices[0].delta.content);
          }
        }

        // --- Process completed tool calls after stream ends ---
        toolCalls = Object.values(currentToolCallFragments); // Convert fragments object to array
        if (toolCalls.length > 0) {
          const toolResults = [];
          for (const toolCall of toolCalls) {
            // Ensure arguments are valid JSON before parsing
            let args;
            try {
              args = JSON.parse(toolCall.function.arguments || '{}');
            } catch (parseError) {
              console.error('Failed to parse tool call arguments:', toolCall.function.arguments, parseError);
              // Handle error: maybe skip this tool call or send an error message
              continue; // Skip this tool call
            }

            if (toolCall.function.name === 'performCustomSearch') {
              console.log('haha:args search', args)
              const searchResults = await performCustomSearch(args.query);
              toolResults.push({
                tool_call_id: toolCall.id,
                role: 'tool',
                name: toolCall.function.name,
                content: JSON.stringify(
                  searchResults.results.map(item => {
                    const { content, ...rest } = item;
                    return rest;
                  })
                )
              });
              source = searchResults.source;
              resultsSearch = searchResults.results;
            }
            // Add handling for other potential tool names here
          }

          // Khi có tool call, thêm kết quả vào prompt và gọi lại askOpenAICompatible
          if (toolResults.length > 0) {
            promptContext = promptContext.concat(toolResults);
            await askOpenAICompatible(model); // Recursive call without tools initially
          } else {
            emitEndMessage(); // No valid tool results to process
          }
        } else {
          // No tool calls, check if response exists or queue has items
          if (response.trim() || translationQueue.length) {
            emitEndMessage();
          } else {
            // Handle cases where the stream ended without content or tool calls
            console.log('Stream ended without response or tool calls, retrying...');
            await askOpenAICompatible(model); // Retry the call
          }
        }
        // --- End Tool Call Processing ---

      } else {
        console.log('haha:chatCompletion (non-stream or error)', chatCompletion)
        // Handle non-stream response or error case - maybe retry?
        handleError(new Error('Non-stream response or unexpected completion object'));
      }
    } catch (err) {
      // Improved Error Handling
      console.error('askOpenAICompatible Error:', err);
      let userMessage = messageError; // Default error message
      if (err.error?.code === 'tool_use_failed') {
        console.log('haha:tool_use_failed', err)
        // Maybe retry without tools? Or just inform the user.
        userMessage = 'Lỗi khi sử dụng công cụ tìm kiếm. Đang thử lại...';
        // Decide whether to retry automatically or just fail
        // For now, just fail with a specific message
        handleError(err, 'Lỗi khi sử dụng công cụ tìm kiếm. Vui lòng thử lại.');
        // Or retry: askOpenAICompatible(model, false); // Retry without tools
      } else if (err.error?.code === 'rate_limit_exceeded' || err.status === 429) {
        userMessage = 'Bạn đang hỏi quá nhiều trong thời gian này. Bạn vui lòng đợi ít phút rồi thử lại. Xin cảm ơn.';
        handleError(err, userMessage);
      } else if (err.error?.code === 'context_length_exceeded') {
        userMessage = 'Câu hỏi hoặc ngữ cảnh quá dài. Vui lòng rút gọn và thử lại.';
        handleError(err, userMessage);
      } else {
        // Generic error
        handleError(err);
      }
    }
  };

  const handleError = (err, msg = messageError) => {
    console.log('haha:error', err)
    error = err;
    response = msg;
    emitEndMessage();
  };

  const askRAG = async () => {
    console.log('haha1.1:askRAG', promptContext)
    const serverRAG = objModel.urlRAG || 'http://203.171.31.42:8001'
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverRAG}/query_stream`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        source = 'RAG';
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  const classify = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/classify`,
        data: {
          messages: promptContext
        },
        timeout: 60000
      });

      const { destination } = resAxios.data.category;
      console.log('haha:destination', destination)
      if (destination === 'RAG') {
        askRAG();
      } else if (destination === 'Search') {
        toolChoice = {
          type: 'function',
          function: { name: 'performCustomSearch' }
        };
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true, toolChoice);
      } else {
        askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
      }
    } catch (err) {
      console.log('haha:err clas', err)
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  }

  // --- Xây dựng promptContext từ lịch sử hội thoại ---
  let promptContext = [{ role: 'user', content: text }];
  try {
    // Kiểm tra các tham số bắt buộc
    if (!deviceId || !serverChatbot || !conversationId || !modelId) {
      throw new Error(`code: ${CONSTANTS.CODE.WRONG_PARAMS}`);
    }

    // Lấy thông tin model
    if (modelId) {
      const modelDoc = await Model.findOne({ _id: modelId }, 'name url provider modelUse useGroq reasoning urlRAG')
        .lean()
        .exec();
      if (!modelDoc || !modelDoc.url) throw new Error('Model not found');
      objModel = modelDoc;
      serverChatbot = objModel.url;
    }

    // Kiểm tra sự tồn tại của conversation và cập nhật updatedAt
    const conv = await Conversation.findOneAndUpdate(
      { _id: conversationId },
      { updatedAt: Date.now() }
    )
      .lean()
      .exec();
    if (!conv) throw new Error(`Conversation not found`);
    conversation = conv;

    // Lấy 5 chat gần nhất làm context
    const chats = await Chat.find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec();
    if (chats.length) {
      const context = chats
        .reverse()
        .map(chat => ({
          role: 'user',
          content: chat.summaryQuestion ? chat.summaryQuestion.trim() : chat.question.trim()
        }))
        .flatMap((userMessage, index) => [
          userMessage,
          {
            role: 'assistant',
            content: chats[index].summaryAnswer ? chats[index].summaryAnswer.trim() : chats[index].answer.trim()
          }
        ]);
      promptContext = context.concat(promptContext);
    }

    // Determine which function to call based on provider
    if (objModel.provider === 'vietanh') {
      askVietAnh();
    } else if (objModel.provider === 'groq' || objModel.provider === 'openrouter' || objModel.useGroq) { // Added 'openrouter'
      // Use the OpenAI compatible function for Groq, OpenRouter, or if useGroq is true
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    } else if (objModel.provider === 'heyu' && !objModel.urlRAG) { // Specific check for 'heyu' without RAG
      ask(); // Original 'heyu' ask function
    } else if (objModel.provider === 'heyu' && objModel.urlRAG) { // Specific check for 'heyu' with RAG
      classify(); // Use classify which might lead to askRAG or askOpenAICompatible
    } else {
      // Fallback or handle unknown providers - maybe default to OpenAICompatible or throw error
      console.warn(`Unknown or unhandled provider: ${objModel.provider}. Falling back to OpenAICompatible.`);
      askOpenAICompatible(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
    res.json({ code: CONSTANTS.CODE.SUCCESS });
  } catch (err) {
    console.error(err);
    res.json({ code: CONSTANTS.CODE.SYSTEM_ERROR, message: MESSAGES.SYSTEM.ERROR });
  }
};
