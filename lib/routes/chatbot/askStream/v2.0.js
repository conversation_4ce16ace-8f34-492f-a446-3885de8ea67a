const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')
const axios = require('axios');
// const Groq = require('groq-sdk');
const OpenAI = require('openai');
const translate = require("translate-google");
const { detect } = require("langdetect");


module.exports = (req, res) => {

  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const model = req.body.model || '';
  let max_new_tokens = _.get(req, 'body.max_new_tokens', 1028);
  let temperature = _.get(req, 'body.temperature', 0.5);
  let top_k = _.get(req, 'body.top_k', 100);
  let top_p = _.get(req, 'body.top_p', 0.9);

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let source = '';
  let text = _.get(req, 'body.text', '').trim();
  let systemPrompt = "Bạn là một trợ lý thông minh, nhiệt tình và trung thực. Hãy luôn trả lời một cách hữu ích nhất có thể, đồng thời giữ an toàn.\n"
  systemPrompt += "Câu trả lời của bạn không nên chứa bất kỳ nội dung gây hại, phân biệt chủng tộc, phân biệt giới tính, độc hại, nguy hiểm hoặc bất hợp pháp nào. Hãy đảm bảo rằng các câu trả lời của bạn không có thiên kiến xã hội và mang tính tích cực.\n"
  systemPrompt += "Nếu một câu hỏi không có ý nghĩa hoặc không hợp lý về mặt thông tin, hãy giải thích tại sao thay vì trả lời một điều gì đó không chính xác. Nếu bạn không biết câu trả lời cho một câu hỏi, hãy trẳ lời là bạn không biết và vui lòng không chia sẻ thông tin sai lệch.\n"
  systemPrompt += "Hãy trả lời 100% bằng tiếng Việt, không sử dụng từ hoặc ký tự từ các ngôn ngữ khác. Nếu không có từ tương đương trong tiếng Việt, hãy giải thích thay vì dùng từ nước ngoài.\n"
  // systemPrompt += "Ưu tiên kiến thức tôi cung cấp, kết hợp kiến thức hiện có của bạn để tăng thêm chiều sâu và tính phù hợp cho câu trả lời."
  systemPrompt += "Nếu cần thiết sử dụng bất cứ công cụ nào, bạn có thể sử dụng nó mà không cần xin phép."
  let promptContext = [{ role: 'user', content: text }];
  let response = '';
  let reasoning = '';
  const createdAt = Date.now()
  const _id = mongoose.Types.ObjectId();
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn'
  let error = null;
  let objModel = {};
  let queue = []; // Hàng đợi xử lý chunk theo thứ tự
  let processing = false; // Tránh xử lý song song gây loạn thứ tự
  let ended = false;

  const checkParams = (next) => {
    if (!deviceId || !serverChatbot || !conversationId || !model) {
      handleError(new Error(`code: ${CONSTANTS.CODE.WRONG_PARAMS}`));
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getModel = (next) => {
    if (!model) {
      return next();
    }

    Model
      .findOne({
        _id: model
      }, 'name url provider modelUse useGroq reasoning')
      .lean()
      .exec((err, result) => {
        if (err || !result || !result.url) {
          err = err || new Error('Model not found');
          handleError(err);
          return next(err);
        }

        objModel = result;
        serverChatbot = objModel.url;

        next();
      })
  }

  const checkConversationExists = (next) => {
    Conversation
      .findOneAndUpdate({
        _id: conversationId
      }, {
        updatedAt: Date.now()
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          handleError(err);
          return next(err);
        }

        if (!data) {
          handleError(new Error(`Conversation not found`));
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        getContext(next);
      });
  };

  const getContext = (next) => {
    Chat.find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec(async (err, chats) => {
        if (err) {
          handleError(err);
          return next(err);
        }

        if (chats.length) {
          // let context = chats
          //   .reverse()
          //   .map((chat) => `user: ${chat.question.trim().replace(/[\r\n]+/g," ")}\nassistant: ${chat.answer.trim().replace(/[\r\n]+/g," ")}`)
          //   .join('\n');
          let context = chats.reverse().map((chat) => ({
            role: 'user',
            content: chat.summaryQuestion ? chat.summaryQuestion.trim() : chat.question.trim()
          })).flatMap((userMessage, index) => [
            userMessage,
            {
              role: 'assistant',
              content: chats[index].summaryAnswer ? chats[index].summaryAnswer.trim() : chats[index].answer.trim()
            },
          ]);

          // const summary = await summarizeContext(context);
          // let summary = context;
          // promptContext = `assistant: Tôi là trợ lý AI, sẵn sàng hỗ trợ bạn với mọi câu hỏi.\n` + summary + `\nuser:${text}\nassistant:`;

          // context = await summarizedContentByParts(context);
          promptContext = context.concat(promptContext);
          // promptContext = `### Câu hỏi: Dựa vào đoạn hội thoại sau đây:\n${summary}\nHãy trả lời câu hỏi: ${text}\n### Trả lời:`;
        }

        console.log('----')
        console.log(promptContext);
        console.log('----')

        if (objModel.provider !== 'heyu' || objModel.useGroq) {
          promptContext.unshift({ role: 'system', content: systemPrompt });
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
        } else {
          ask();
        }

        next(null, { code: CONSTANTS.CODE.SUCCESS });
      });
  };

  const randomNumber = (max) => {
    return Math.floor(Math.random() * max);
  }

  const summarizedContentByParts = async (messages) => {
    const summaries = [];

    for (const message of messages) {
      let summary = message.content;
      if (summary.length > 200) {
        summary = await summarizeContext(objModel.modelUse ? objModel.modelUse : objModel, summary);
      }
      summaries.push({ role: message.role, content: summary });
    }

    return summaries;
  };

  const summarizeContext = async (model, message) => {
    message = message.trim();

    // try {
    //   const response = await axios.post(`${serverChatbot}/generate`, {
    //     // prompt: promptContext,
    //     // prompt: `### Câu hỏi: Đây là một cuộc hội thoại giữa người dùng và chatbot. Tóm tắt ngắn gọn nội dung chính:\n${context}\n### Trả lời:`,
    //     messages: [
    //       { role: 'system', content: 'Bạn là một trợ lý AI thông minh, được thiết kế để tóm tắt văn bản một cách ngắn gọn nhưng vẫn giữ đủ ý chính. Hãy đảm bảo rằng bản tóm tắt bao gồm các điểm chính và không bỏ sót thông tin quan trọng.' },
    //       { role: 'user', content: `Tóm tắt văn bản sau và chỉ đưa ra văn bản đã được tóm tắt: ${message}` }
    //     ],
    //     temperature,
    //     top_k,
    //     top_p
    //   }, {
    //     timeout: 60000,
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //   });

    //   response.data = response.data.replace('Tóm tắt:', '');

    //   return response.data.trim() || message;
    // } catch (error) {
    //   console.error('Tóm tắt thất bại:', error);

    //   return message;
    // }

    const indexKey = randomNumber(config.apiKey[model.provider].length);
    console.log('haha:key', indexKey, config.apiKey[model.provider][indexKey])
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url || 'https://api.groq.com/openai/v1'
    });

    try {
      const objCreate = {
        messages: [
          { role: 'system', content: 'Bạn là một trợ lý AI thông minh, được thiết kế để tóm tắt văn bản một cách ngắn gọn nhưng vẫn giữ đủ ý chính. Hãy đảm bảo rằng bản tóm tắt bao gồm các điểm chính và không bỏ sót thông tin quan trọng.' },
          { role: 'user', content: `Tóm tắt văn bản sau và chỉ đưa ra văn bản đã được tóm tắt: ${message}` }
        ],
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 4960,
        top_p: top_p || 1,
        stream: false,
        stop: null
      }

      if (model.reasoning) {
        objCreate.reasoning_format = 'hidden';
      }

      const chatCompletion = await openai.chat.completions.create(objCreate);

      return chatCompletion.choices[0].message.content.trim() || message;
    } catch (err) {
      console.error('Tóm tắt thất bại:', err);

      return message;
    }
  };

  const ask = async () => {
    console.log('haha:ask', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/query`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          // max_new_tokens,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });

      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        if (chunk.toString() === 'sonden') {
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
        } else {
          emitNewMessage(chunk.toString());
        }
      });
      stream.on('end', () => {
        console.log('haha:endrag', response)

        if (response.trim() === 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn.') {
          response = '';
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }

        // if (response) {
        //   source = 'rag';
        // }

        emitEndMessage();
      });
      stream.on('error', (err) => {
        console.log('haha:ask:error', err)
        // handleError(err);
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      console.log('haha:ask:err', err)
      // handleError(err);
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  }

  const askGroq = async (model, useTools = false) => {
    console.log('haha:askGroq', promptContext)
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    console.log('haha:key', indexKey, config.apiKey[model.provider][indexKey])
    // const groq = new Groq({ apiKey: config.apiKey.groq });
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url
    });

    const tools = [
      {
        type: 'function',
        function: {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        }
      }
    ];

    try {
      const objCreate = {
        messages: promptContext,
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 4960,
        top_p: top_p || 1,
        stream: true,
        stop: null,
        // reasoning_format: "hidden"
        // Thêm tools vào yêu cầu
        // tools,
        // tool_choice: 'auto' // Groq sẽ tự động quyết định khi nào cần dùng tool
      }

      if (model.reasoning) {
        objCreate.reasoning_format = model.reasoning;
      }

      if (useTools) {
        objCreate.tools = tools;
        objCreate.tool_choice = 'auto';
      }
      const chatCompletion = await openai.chat.completions.create(objCreate);

      let toolCalls = [];

      if (chatCompletion && typeof chatCompletion[Symbol.asyncIterator] === 'function') {
        for await (const chunk of chatCompletion) {
          if (chunk?.choices[0]?.delta?.tool_calls?.length) {
            toolCalls = toolCalls.concat(chunk.choices[0].delta.tool_calls);
            console.log('haha:searching')
            io.to(deviceId).emit('searching', {
              text: 'Đang tìm kiếm thêm thông tin...'
            });
          }

          let reasoning = chunk?.choices[0]?.delta?.reasoning;
          if (reasoning) {
            emitNewReasoning(reasoning);
          }

          let content = chunk?.choices[0]?.delta?.content;
          if (content) {
            emitNewMessage(content);
          }
        }

        console.log('haha:toolCalls', toolCalls)
        // Xử lý nếu có tool call
        if (toolCalls.length > 0) {
          const toolResults = [];
          for (const toolCall of toolCalls) {
            if (toolCall.function.name === 'performCustomSearch') {
              const args = JSON.parse(toolCall.function.arguments);
              const searchResults = await performCustomSearch(args.query);
              // Gửi kết quả tìm kiếm về cho Groq để nó có thể sử dụng trong phản hồi
              toolResults.push({
                tool_call_id: toolCall.id,
                role: 'tool',
                name: toolCall.function.name,
                content: JSON.stringify(searchResults.results.map(item => {
                  const { content, ...rest } = item;
                  return rest;
                }))
              });
              source = searchResults.source;

              // Chỉ gọi lại askGroq một lần với tất cả kết quả của tool call
              if (toolResults.length > 0) {
                promptContext = promptContext.concat(toolResults); // Thêm kết quả vào promptContext
                await askGroq(model); // Gọi lại khi có các kết quả từ tool call
              } else {
                emitEndMessage();
              }
            }
          }
        } else {
          if (response.trim()) {
            emitEndMessage();
          } else {
            await askGroq(model);
          }
        }
      } else {
        console.error('chatCompletion không phải là một AsyncIterable');

        await askGroq(model);
      }
    } catch (err) {
      console.log('haha:askGroq:errr', err)

      if (err.error.code === 'tool_use_failed') {
        await askGroq(model);
      } else if (err.error.code === 'rate_limit_exceeded') {
        handleError(err, 'Bạn đang hỏi quá nhiều trong thời gian này. Bạn vui lòng đợi ít phút rồi thử lại. Xin cảm ơn.');
      } else {
        handleError(err);
      }
    }
  };

  const processQueue = async () => {
    if (processing || queue.length === 0) return;
    processing = true;

    while (queue.length > 0) {
      let { chunk, resolve } = queue.shift(); // Lấy chunk đầu tiên và xóa khỏi hàng đợi
      await resolve(); // Thực hiện xử lý chunk
    }

    processing = false;
  };

  const getMostLikelyLanguage = (detectedLangs) => {
    if (!detectedLangs) {
      return 'vi';
    }

    return detectedLangs.reduce((max, lang) => (lang.prob > max.prob ? lang : max), detectedLangs[0]).lang;
  }

  const translateNonVietnameseSentences = async (text) => {
    console.log('haha:text', text)
    let translatedText = '';

    // Kiểm tra nếu có **ký tự tượng hình**
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);

    if (!hasNonLatinChars) {
      return text; // Nếu không có ký tự tượng hình, giữ nguyên
    }

    try {
      const translatedWord = await translate(text, { to: 'vi' });
      translatedText = ' ' + translatedWord.toLowerCase();
    } catch (error) {
      console.error(`Lỗi dịch từ: ${text}`, error);
    }

    console.log('haha:translatedText', text, translatedText)
    return translatedText;
  };

  const emitNewReasoning = (chunk) => {
    const processChunk = async () => {
      let translatedText = await translateNonVietnameseSentences(chunk);
      reasoning += translatedText;
      reasoning = reasoning.replace('undefined', '')
      // remove trợ lý. Trợ lý:, assistant:, :Assistant
      reasoning = reasoning.replace(/trợ lý:/g, "");
      reasoning = reasoning.replace(/Trợ lý:/g, "");
      reasoning = reasoning.replace(/assistant:/g, "");
      reasoning = reasoning.replace(/Assistant:/g, "");
      reasoning = reasoning.replace(/user:/g, "");
      reasoning = reasoning.replace(/User:/g, "");

      io.to(deviceId).emit('newReasoning', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        answer: response,
        createdAt,
        source,
        model: objModel.name
      });
    };

    queue.push({
      chunk,
      resolve: processChunk
    });
    if (!processing) processQueue(); // Bắt đầu xử lý nếu chưa đang xử lý
  };

  const emitNewMessage = (chunk) => {
    const processChunk = async () => {
      let translatedText = await translateNonVietnameseSentences(chunk);
      response += translatedText;
      response = response.replace('undefined', '')
      // remove trợ lý. Trợ lý:, assistant:, :Assistant
      response = response.replace(/trợ lý:/g, "");
      response = response.replace(/Trợ lý:/g, "");
      response = response.replace(/assistant:/g, "");
      response = response.replace(/Assistant:/g, "");
      response = response.replace(/user:/g, "");
      response = response.replace(/User:/g, "");

      io.to(deviceId).emit('newMessage', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        answer: response,
        createdAt,
        source,
        model: objModel.name
      });
    };

    queue.push({
      chunk,
      resolve: processChunk
    });
    if (!processing) processQueue(); // Bắt đầu xử lý nếu chưa đang xử lý
  };

  const handleError = (err, msg = messageError) => {
    error = err
    response = msg

    emitEndMessage();
  }

  const emitEndMessage = () => {
    if (ended) {
      return;
    }

    console.log('haha:endMessage')
    ended = true;
    io.to(deviceId).emit('endMessage', {
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      reasoning,
      answer: response,
      createdAt,
      source,
      model: objModel.name
    });

    createCollection();
  }

  const createCollection = async () => {
    const model = objModel.modelUse ? objModel.modelUse : objModel;
    let summaryQuestion = text.trim();
    if (summaryQuestion.length > 200) {
      summaryQuestion = await summarizeContext(model, summaryQuestion);
    }

    let summaryAnswer = response.trim();
    if (summaryAnswer.length > 200) {
      summaryAnswer = await summarizeContext(model, summaryAnswer);
    }

    Chat
      .create({
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        summaryQuestion,
        reasoning: reasoning.trim(),
        answer: response.trim(),
        summaryAnswer,
        createdAt,
        error,
        source,
        model: objModel._id
      }, (err, result) => { })
  }

  const performCustomSearch = async (query, num_results = 10, time_option = 'last_month') => {
    try {
      const res = await axios.get(`http://203.171.31.42:5000/search`, {
        params: {
          question: query,
          num_results, // Hoặc bất kỳ số lượng kết quả nào bạn muốn
          time_option
        }
      });
      return res.data;
    } catch (error) {
      console.error('Error performing custom search:', error);
      return null;
    }
  }

  async.waterfall([
    checkParams,
    getModel,
    checkConversationExists,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}