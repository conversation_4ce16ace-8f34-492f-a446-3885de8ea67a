const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')
const axios = require('axios');
// const Groq = require('groq-sdk');
const OpenAI = require('openai');
const translate = require("translate-google");
const { detect } = require("langdetect");

module.exports = async (req, res) => {
  // --- C<PERSON>u hình SSE cho response ---
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  // --- <PERSON><PERSON><PERSON> c<PERSON>c tham số từ request ---
  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const modelId = req.body.model || '';
  const location = req.body.location || '';
  const text = _.get(req, 'body.text', '').trim();
  const max_new_tokens = _.get(req, 'body.max_new_tokens', 1028);
  const temperature = _.get(req, 'body.temperature', 0.5);
  const top_k = _.get(req, 'body.top_k', 100);
  const top_p = _.get(req, 'body.top_p', 0.9);

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let response = '';
  let reasoning = '';
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn';
  let error = null;
  let objModel = {};
  let source = '';
  let resultsSearch = [];
  let startReasoning;
  let endReasoning;
  let timeReasoning;
  let textReasoning;
  const translationCache = new Map();
  let conversation;

  // Hàng đợi xử lý các chunk để đảm bảo thứ tự phát
  let translationQueue = [];
  let processingQueue = false;
  let ended = false;

  // --- Định nghĩa system prompt dưới dạng 1 chuỗi được ghép từ mảng ---
  const systemPrompt = [
    "Bạn là một trợ lý thông minh có tên là HeyAI, được phát triển bởi các kỹ sư AI Việt Nam. Hãy luôn trả lời một cách hữu ích nhất có thể, đồng thời giữ an toàn.",
    "Hãy trả lời 100% bằng tiếng Việt, không sử dụng từ hoặc ký tự từ các ngôn ngữ khác. Nếu không có từ tương đương trong tiếng Việt, hãy giải thích thay vì dùng từ nước ngoài.",
    `Nếu cần thiết sử dụng bất cứ công cụ nào, bạn có thể sử dụng nó mà không cần xin phép.${location ? ` Vị trí người dùng: ${location}` : ''}`
  ].join('\n');

  // --- Các hàm tiện ích ---

  // Hàm chuyển các ký tự không phải tiếng Việt sang tiếng Việt nếu cần
  const translateNonVietnameseSentences = async (text) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    // Kiểm tra xem văn bản có chứa ký tự không phải Latin không
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);
    if (!hasNonLatinChars) return text;

    // Nếu văn bản đã được dịch trước đó, trả về kết quả cache
    if (translationCache.has(text)) {
      return translationCache.get(text);
    }

    try {
      const translated = await translate(text, { to: 'vi' });
      const translatedText = ' ' + translated.toLowerCase();
      // Lưu kết quả dịch vào cache
      translationCache.set(text, translatedText);
      return translatedText;
    } catch (error) {
      console.error(`Lỗi dịch: ${text}`, error);
      return text;
    }
  };

  // Hàm thêm một tác vụ vào hàng đợi và kích hoạt xử lý
  const enqueueTranslation = (task) => {
    translationQueue.push(task);
    processTranslationQueue();
  };

  // Hàm xử lý hàng đợi tuần tự
  const processTranslationQueue = async () => {
    if (processingQueue) return;
    processingQueue = true;

    while (translationQueue.length > 0) {
      const task = translationQueue.shift();
      try {
        await task();
      } catch (err) {
        console.error('Lỗi xử lý hàng đợi:', err);
      }
    }

    processingQueue = false;
  };

  // --- Hàm gửi sự kiện qua SSE ---
  const emitToDevice = (event, payload) => {
    res.write(`event: ${event}\n`);
    res.write(`data: ${JSON.stringify(payload)}\n\n`);
  };

  // Hàm phát reasoning, bao gồm việc dịch và format chuỗi
  const emitNewReasoning = (chunk) => {
    console.log('haha:reasioning', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      reasoning += translated;
      reasoning = reasoning.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      emitToDevice('newReasoning', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning: 'Đang suy nghĩ...',
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Hàm phát nội dung message mới (phần trả lời)
  const emitNewMessage = (chunk) => {
    console.log('haha:message', chunk)
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      response += translated;
      // Loại bỏ các từ thừa
      response = response.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      console.log('push stream to client:', translated)
      emitToDevice('newMessage', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning,
        answer: response,
        createdAt,
        source,
        resultsSearch,
        model: objModel.name
      });
    });
  };

  // Khi kết thúc toàn bộ stream, phát thông điệp kết thúc và lưu vào CSDL
  const emitEndMessage = async () => {
    // Đợi cho đến khi hàng đợi hoàn toàn trống
    console.log('haha:end', translationQueue.length, processingQueue)
    while (translationQueue.length > 0 || processingQueue) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    if (ended) return;
    console.log('haha1.1:emitEndMessage')
    ended = true;
    emitToDevice('endMessage', {
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      reasoning,
      textReasoning,
      answer: response,
      createdAt,
      source,
      resultsSearch,
      model: objModel.name
    });
    createCollection();
    res.end();
  };

  // Lưu lịch sử chat vào database
  const createCollection = async () => {
    const usedModel = objModel.modelUse ? objModel.modelUse : objModel;
    const [summaryQuestion, summaryAnswer] = await Promise.all([
      (async () => {
        let s = text.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })(),
      (async () => {
        let s = response.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })()
    ]);
    Chat.create({
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      summaryQuestion,
      reasoning: reasoning.trim(),
      textReasoning,
      answer: response.trim(),
      summaryAnswer,
      createdAt,
      error,
      source,
      resultsSearch,
      model: objModel._id
    }, (err) => {
      if (err) console.error('Lỗi lưu chat:', err);
    });
  };

  // Thực hiện tìm kiếm tùy chỉnh (custom search)
  const performCustomSearch = async (query, num_results = 10, time_option = 'last_month') => {
    try {
      const resAxios = await axios.get(`http://203.171.31.42:5000/search`, {
        params: { question: query, num_results, time_option }
      });
      return resAxios.data;
    } catch (error) {
      console.error('Error performing custom search:', error);
      return null;
    }
  };

  // Hàm tạo số ngẫu nhiên (dùng để lấy api key)
  const randomNumber = (max) => Math.floor(Math.random() * max);

  // Hàm tóm tắt nội dung khi chuỗi quá dài
  const summarizeContext = async (model, message) => {
    console.log('haha:summarizeContext', model)
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url || 'https://api.groq.com/openai/v1'
    });
    try {
      const objCreate = {
        messages: [
          {
            role: 'system',
            content: 'Tóm tắt văn bản dưới đây một cách ngắn gọn, súc tích, giữ đủ ý chính và không cung cấp thêm bất kỳ thông tin hay lời giải thích nào khác:'
          },
          { role: 'user', content: `${message}` }
        ],
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 500,
        top_p: top_p || 1,
        stream: false,
        stop: null,
        service_tier: 'flex',
      };
      if (model.reasoning) objCreate.reasoning_format = 'hidden';
      const chatCompletion = await openai.chat.completions.create(objCreate);
      return chatCompletion.choices[0].message.content.trim() || message;
    } catch (err) {
      console.error('Tóm tắt thất bại:', err);
      return message;
    }
  };

  // --- Các hàm gọi OpenAI / Chatbot ---

  // Phiên bản gọi API truyền thống (không dùng tools)
  const ask = async () => {
    console.log('haha1.1:ask', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/query`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản gọi API của Việt Anh
  const askVietAnh = async () => {
    console.log('haha1.1:askVietAnh', Date.now(), promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream_public`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        if (!response) {
          console.log('haha:time', Date.now())
        }
        console.log('haha:data', chunk.toString())
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        console.log('haha:end')
        emitEndMessage();
      });
      stream.on('error', () => {
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản dùng OpenAI với khả năng sử dụng tools (Groq)
  const askGroq = async (model, useTools = false, toolChoice = 'auto') => {
    if (typeof toolChoice === 'object') {
      console.log('Đang tìm kiếm thêm thông tin...1')
      emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
    }
    const hasSystemRole = promptContext.some(message => message.role === 'system');
    if (!hasSystemRole) {
      promptContext.unshift({ role: 'system', content: systemPrompt });
    }
    const hasToolRole = promptContext.some(message => message.role === 'tool');
    if (hasToolRole) {
      console.log('Đang tổng hợp thông tin...')
      emitToDevice('searching', { text: 'Đang tổng hợp thông tin...' });
    }
    console.log('haha1.1:askGroq', model, promptContext)
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url
    });
    const tools = [
      {
        type: 'function',
        function: {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        }
      }
    ];
    try {
      const objCreate = {
        messages: promptContext,
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 1500,
        top_p: top_p || 1,
        stream: true,
        stop: null,
        service_tier: 'flex',
      };
      if (model.reasoning) objCreate.reasoning_format = model.reasoning;
      if (useTools) {
        objCreate.tools = tools;
        objCreate.tool_choice = toolChoice;
      }
      const chatCompletion = await openai.chat.completions.create(objCreate);
      let toolCalls = [];
      if (chatCompletion && typeof chatCompletion[Symbol.asyncIterator] === 'function') {
        for await (const chunk of chatCompletion) {
          if (chunk?.choices[0]?.delta?.tool_calls?.length) {
            toolCalls = toolCalls.concat(chunk.choices[0].delta.tool_calls);
            console.log('Đang tìm kiếm thêm thông tin...')
            emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
          }
          if (chunk?.choices[0]?.delta?.reasoning) {
            if (!reasoning) {
              startReasoning = Date.now();
            }
            emitNewReasoning(chunk.choices[0].delta.reasoning);
          }
          if (chunk?.choices[0]?.delta?.content) {
            if (!response && startReasoning) {
              endReasoning = Date.now();
              timeReasoning = endReasoning - startReasoning;
              textReasoning = `Suy nghĩ trong ${Math.floor(timeReasoning / 1000) || 1} giây`
            }
            emitNewMessage(chunk.choices[0].delta.content);
          }
        }
        if (toolCalls.length > 0) {
          const toolResults = [];
          for (const toolCall of toolCalls) {
            if (toolCall.function.name === 'performCustomSearch') {
              const args = JSON.parse(toolCall.function.arguments);
              console.log('haha:args search', args)
              const searchResults = await performCustomSearch(args.query);
              toolResults.push({
                tool_call_id: toolCall.id,
                role: 'tool',
                name: toolCall.function.name,
                content: JSON.stringify(
                  searchResults.results.map(item => {
                    const { content, ...rest } = item;
                    return rest;
                  })
                )
              });
              source = searchResults.source;
              resultsSearch = searchResults.results;
              // Khi có tool call, thêm kết quả vào prompt và gọi lại askGroq
              if (toolResults.length > 0) {
                promptContext = promptContext.concat(toolResults);
                await askGroq(model);
              } else {
                emitEndMessage();
              }
            }
          }
        } else {
          if (response.trim()) {
            emitEndMessage();
          } else {
            await askGroq(model);
          }
        }
      } else {
        console.log('haha:chatCompletion', chatCompletion)
        askGroq(model);
      }
    } catch (err) {
      if (err.error.code === 'tool_use_failed') {
        console.log('haha:tool_use_failed', err)
        askGroq(model);
      } else if (err.error.code === 'rate_limit_exceeded') {
        handleError(err, 'Bạn đang hỏi quá nhiều trong thời gian này. Bạn vui lòng đợi ít phút rồi thử lại. Xin cảm ơn.');
      } else {
        handleError(err);
      }
    }
  };

  const handleError = (err, msg = messageError) => {
    error = err;
    response = msg;
    emitEndMessage();
  };

  const askRAG = async () => {
    console.log('haha1.1:askRAG', promptContext)
    const serverRAG = objModel.urlRAG || 'http://203.171.31.42:8001'
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverRAG}/query_stream`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        source = 'RAG';
        const chunkStr = chunk.toString();
        emitNewMessage(chunkStr);
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  const classify = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/classify`,
        data: {
          messages: promptContext
        },
        timeout: 60000
      });

      const { destination } = resAxios.data.category;
      console.log('haha:destination', destination)
      if (destination === 'RAG') {
        askRAG();
      } else if (destination === 'Search') {
        toolChoice = {
          type: 'function',
          function: { name: 'performCustomSearch' }
        };
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true, toolChoice);
      } else {
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      }
    } catch (err) {
      console.log('haha:err clas', err)
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  }

  // --- Xây dựng promptContext từ lịch sử hội thoại ---
  let promptContext = [{ role: 'user', content: text }];
  try {
    // Kiểm tra các tham số bắt buộc
    if (!deviceId || !serverChatbot || !conversationId || !modelId) {
      throw new Error(`code: ${CONSTANTS.CODE.WRONG_PARAMS}`);
    }

    // Lấy thông tin model
    if (modelId) {
      const modelDoc = await Model.findOne({ _id: modelId }, 'name url provider modelUse useGroq reasoning urlRAG')
        .lean()
        .exec();
      if (!modelDoc || !modelDoc.url) throw new Error('Model not found');
      objModel = modelDoc;
      serverChatbot = objModel.url;
    }

    // Kiểm tra sự tồn tại của conversation và cập nhật updatedAt
    const conv = await Conversation.findOneAndUpdate(
      { _id: conversationId },
      { updatedAt: Date.now() }
    )
      .lean()
      .exec();
    if (!conv) throw new Error(`Conversation not found`);
    conversation = conv;

    // Lấy 5 chat gần nhất làm context
    const chats = await Chat.find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec();
    if (chats.length) {
      const context = chats
        .reverse()
        .map(chat => ({
          role: 'user',
          content: chat.summaryQuestion ? chat.summaryQuestion.trim() : chat.question.trim()
        }))
        .flatMap((userMessage, index) => [
          userMessage,
          {
            role: 'assistant',
            content: chats[index].summaryAnswer ? chats[index].summaryAnswer.trim() : chats[index].answer.trim()
          }
        ]);
      promptContext = context.concat(promptContext);
    }

    // Nếu provider không phải 'heyu' hoặc dùng Groq, thêm system prompt và gọi askGroq
    if (objModel.provider === 'vietanh') {
      askVietAnh();
    } else if (objModel.provider !== 'heyu' || objModel.useGroq) {
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    } else {
      classify();
    }

    // Không gọi res.json vì dữ liệu sẽ được gửi qua SSE
    // Bạn có thể gửi một sự kiện ban đầu nếu muốn, ví dụ:
    // emitToDevice('init', { code: CONSTANTS.CODE.SUCCESS, message: 'Kết nối thành công' });
  } catch (err) {
    console.error(err);
    emitToDevice('error', { code: CONSTANTS.CODE.SYSTEM_ERROR, message: MESSAGES.SYSTEM.ERROR });
    res.end();
  }
};
