const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')
const axios = require('axios');
// const Groq = require('groq-sdk');
const OpenAI = require('openai');
const translate = require("translate-google");
const { detect } = require("langdetect");

module.exports = async (req, res) => {
  // --- <PERSON><PERSON><PERSON> các tham số từ request ---
  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const modelId = req.body.model || '';
  const text = _.get(req, 'body.text', '').trim();
  const max_new_tokens = _.get(req, 'body.max_new_tokens', 1028);
  const temperature = _.get(req, 'body.temperature', 0.5);
  const top_k = _.get(req, 'body.top_k', 100);
  const top_p = _.get(req, 'body.top_p', 0.9);

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let response = '';
  let reasoning = '';
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn';
  let error = null;
  let objModel = {};
  let source = '';
  let startReasoning;
  let endReasoning;
  let timeReasoning;
  let textReasoning;
  const translationCache = new Map();
  let conversation

  // Hàng đợi xử lý các chunk để đảm bảo thứ tự phát
  let translationQueue = [];
  let processingQueue = false;
  let ended = false;

  // --- Định nghĩa system prompt dưới dạng 1 chuỗi được ghép từ mảng ---
  const systemPrompt = [
    "Bạn là một trợ lý thông minh, nhiệt tình và trung thực. Hãy luôn trả lời một cách hữu ích nhất có thể, đồng thời giữ an toàn.",
    "Hãy trả lời 100% bằng tiếng Việt, không sử dụng từ hoặc ký tự từ các ngôn ngữ khác. Nếu không có từ tương đương trong tiếng Việt, hãy giải thích thay vì dùng từ nước ngoài.",
    "Nếu cần thiết sử dụng bất cứ công cụ nào, bạn có thể sử dụng nó mà không cần xin phép."
  ].join('\n');

  // --- Các hàm tiện ích ---

  // Hàm chuyển các ký tự không phải tiếng Việt sang tiếng Việt nếu cần
  const translateNonVietnameseSentences = async (text) => {
    console.log('haha1.2:newText', text)
    // Kiểm tra xem văn bản có chứa ký tự không phải Latin không
    const hasNonLatinChars = /[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Hangul}\p{Script=Thai}\p{Script=Arabic}]/u.test(text);
    if (!hasNonLatinChars) return text;

    // Nếu văn bản đã được dịch trước đó, trả về kết quả cache
    if (translationCache.has(text)) {
      console.log('haha1.2:translated cache', text, translationCache.get(text))
      return translationCache.get(text);
    }

    try {
      const translated = await translate(text, { to: 'vi' });
      console.log('haha1.2:translated', text, translated)
      const translatedText = ' ' + translated.toLowerCase();
      // Lưu kết quả dịch vào cache
      translationCache.set(text, translatedText);
      return translatedText;
    } catch (error) {
      console.error(`Lỗi dịch: ${text}`, error);
      return text;
    }
  };

  // Hàm thêm một tác vụ vào hàng đợi và kích hoạt xử lý
  const enqueueTranslation = (task) => {
    translationQueue.push(task);
    processTranslationQueue();
  };

  // Hàm xử lý hàng đợi tuần tự
  const processTranslationQueue = async () => {
    if (processingQueue) return;
    processingQueue = true;
    while (translationQueue.length > 0) {
      const task = translationQueue.shift();
      await task(); // Đợi tác vụ hoàn thành mới chuyển sang tác vụ tiếp theo
    }
    processingQueue = false;
  };

  // Hàm phát thông điệp tới client qua socket (giả sử biến toàn cục io đã được khởi tạo)
  const emitToDevice = (event, payload) => {
    io.to(deviceId).emit(event, payload);
  };

  // Hàm phát reasoning, bao gồm việc dịch và format chuỗi
  const emitNewReasoning = (chunk) => {
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      reasoning += translated;
      reasoning = reasoning.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
      emitToDevice('newReasoning', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning: 'Đang suy nghĩ...',
        answer: response,
        createdAt,
        source,
        model: objModel.name
      });
    });
  };

  // Hàm phát nội dung message mới (phần trả lời)
  const emitNewMessage = (chunk) => {
    enqueueTranslation(async () => {
      let translated = await translateNonVietnameseSentences(chunk);
      response += translated;
      // Loại bỏ các từ thừa
      response = response.replace(/undefined/gi, '')
        .replace(/trợ lý:/gi, '')
        .replace(/assistant:/gi, '')
        .replace(/user:/gi, '');
        console.log('push socket to client:', translated)
      emitToDevice('newMessage', {
        _id,
        member: deviceId,
        conversation: conversationId,
        question: text,
        reasoning,
        textReasoning,
        answer: response,
        createdAt,
        source,
        model: objModel.name
      });
    });
  };

  // Khi kết thúc toàn bộ stream, phát thông điệp kết thúc và lưu vào CSDL
  const emitEndMessage = async () => {
    // Đợi cho đến khi hàng đợi hoàn toàn trống
    console.log('haha:end', translationQueue.length, processingQueue)
    while (translationQueue.length > 0 || processingQueue) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    if (ended) return;
    console.log('haha1.2:emitEndMessage')
    ended = true;
    emitToDevice('endMessage', {
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      reasoning,
      textReasoning,
      answer: response,
      createdAt,
      source,
      model: objModel.name
    });
    createCollection();
  };

  // Lưu lịch sử chat vào database
  const createCollection = async () => {
    const usedModel = objModel.modelUse ? objModel.modelUse : objModel;
    const [summaryQuestion, summaryAnswer] = await Promise.all([
      (async () => {
        let s = text.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })(),
      (async () => {
        let s = response.trim();
        if (s.length > 200) {
          s = await summarizeContext(usedModel, s);
        }
        return s;
      })()
    ]);
    Chat.create({
      _id,
      member: deviceId,
      conversation: conversationId,
      question: text,
      summaryQuestion,
      reasoning: reasoning.trim(),
      textReasoning,
      answer: response.trim(),
      summaryAnswer,
      createdAt,
      error,
      source,
      model: objModel._id
    }, (err) => {
      if (err) console.error('Lỗi lưu chat:', err);
    });
  };

  // Thực hiện tìm kiếm tùy chỉnh (custom search)
  const performCustomSearch = async (query, num_results = 10, time_option = 'last_month') => {
    try {
      const res = await axios.get(`http://203.171.31.42:5000/search`, {
        params: { question: query, num_results, time_option }
      });
      return res.data;
    } catch (error) {
      console.error('Error performing custom search:', error);
      return null;
    }
  };

  // Hàm tạo số ngẫu nhiên (dùng để lấy api key)
  const randomNumber = (max) => Math.floor(Math.random() * max);

  // Hàm tóm tắt nội dung khi chuỗi quá dài
  const summarizeContext = async (model, message) => {
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url || 'https://api.groq.com/openai/v1'
    });
    try {
      const objCreate = {
        messages: [
          {
            role: 'system',
            content: 'Tóm tắt văn bản dưới đây một cách ngắn gọn, súc tích, giữ đủ ý chính và không cung cấp thêm bất kỳ thông tin hay lời giải thích nào khác:'
          },
          { role: 'user', content: `${message}` }
        ],
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 4960,
        top_p: top_p || 1,
        stream: false,
        stop: null
      };
      if (model.reasoning) objCreate.reasoning_format = 'hidden';
      const chatCompletion = await openai.chat.completions.create(objCreate);
      return chatCompletion.choices[0].message.content.trim() || message;
    } catch (err) {
      console.error('Tóm tắt thất bại:', err);
      return message;
    }
  };

  // --- Các hàm gọi OpenAI / Chatbot ---

  // Phiên bản gọi API truyền thống (không dùng tools)
  const ask = async () => {
    console.log('haha1.2:ask', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/query`,
        responseType: 'stream',
        data: {
          messages: promptContext,
          temperature,
          top_k,
          top_p
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        const chunkStr = chunk.toString();
        if (chunkStr === 'sonden') {
          console.log('haha:sonden', chunkStr)
          promptContext.unshift({ role: 'system', content: systemPrompt });
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
        } else {
          emitNewMessage(chunkStr);
        }
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          promptContext.unshift({ role: 'system', content: systemPrompt });
          console.log('haha:end', response)
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        console.log('haha:error')
        promptContext.unshift({ role: 'system', content: systemPrompt });
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      console.log('haha:catch', err)
      promptContext.unshift({ role: 'system', content: systemPrompt });
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản gọi API truyền thống (không dùng tools)
  const askVietAnh = async () => {
    console.log('haha1.2:askVietAnh', promptContext)
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream_public`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text
        },
        timeout: 60000
      });
      const stream = resAxios.data;
      stream.on('data', (chunk) => {
        const chunkStr = chunk.toString();
        if (chunkStr === 'sonden') {
          console.log('haha:sonden', chunkStr)
          promptContext.unshift({ role: 'system', content: systemPrompt });
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
        } else {
          emitNewMessage(chunkStr);
        }
      });
      stream.on('end', () => {
        if (response.trim() === messageError) {
          response = '';
          promptContext.unshift({ role: 'system', content: systemPrompt });
          console.log('haha:end', response)
          askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
          return;
        }
        emitEndMessage();
      });
      stream.on('error', () => {
        console.log('haha:error')
        promptContext.unshift({ role: 'system', content: systemPrompt });
        askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
      });
    } catch (err) {
      console.log('haha:catch', err)
      promptContext.unshift({ role: 'system', content: systemPrompt });
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    }
  };

  // Phiên bản dùng OpenAI với khả năng sử dụng tools (Groq)
  const askGroq = async (model, useTools = false) => {
    console.log('haha1.2:askGroq', promptContext)
    const hasToolRole = promptContext.some(message => message.role === 'tool');
    if (hasToolRole) {
      emitToDevice('searching', { text: 'Đang tổng hợp thông tin...' });
    }
    const indexKey = randomNumber(config.apiKey[model.provider].length);
    const openai = new OpenAI({
      apiKey: config.apiKey[model.provider][indexKey],
      baseURL: model.url
    });
    const tools = [
      {
        type: 'function',
        function: {
          name: 'performCustomSearch',
          description: 'Get the latest information from the internet searching',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Question you want to search on internet'
              }
            },
            required: ['query']
          }
        }
      }
    ];
    try {
      const objCreate = {
        messages: promptContext,
        model: model.name || 'llama-3.3-70b-versatile',
        temperature: temperature || 1,
        max_completion_tokens: 4960,
        top_p: top_p || 1,
        stream: true,
        stop: null,
      };
      if (model.reasoning) objCreate.reasoning_format = model.reasoning;
      if (useTools) {
        objCreate.tools = tools;
        objCreate.tool_choice = 'auto';
      }
      const chatCompletion = await openai.chat.completions.create(objCreate);
      let toolCalls = [];
      if (chatCompletion && typeof chatCompletion[Symbol.asyncIterator] === 'function') {
        for await (const chunk of chatCompletion) {
          if (chunk?.choices[0]?.delta?.tool_calls?.length) {
            toolCalls = toolCalls.concat(chunk.choices[0].delta.tool_calls);
            emitToDevice('searching', { text: 'Đang tìm kiếm thêm thông tin...' });
          }
          if (chunk?.choices[0]?.delta?.reasoning) {
            if (!reasoning) {
              startReasoning = Date.now();
            }
            emitNewReasoning(chunk.choices[0].delta.reasoning);
          }
          if (chunk?.choices[0]?.delta?.content) {
            if (!response && startReasoning) {
              endReasoning = Date.now();
              timeReasoning = endReasoning - startReasoning;
              textReasoning = `Suy nghĩ trong ${Math.floor(timeReasoning / 1000) || 1} giây`
            }
            emitNewMessage(chunk.choices[0].delta.content);
          }
        }
        if (toolCalls.length > 0) {
          const toolResults = [];
          for (const toolCall of toolCalls) {
            if (toolCall.function.name === 'performCustomSearch') {
              const args = JSON.parse(toolCall.function.arguments);
              const searchResults = await performCustomSearch(args.query);
              toolResults.push({
                tool_call_id: toolCall.id,
                role: 'tool',
                name: toolCall.function.name,
                content: JSON.stringify(
                  searchResults.results.map(item => {
                    const { content, ...rest } = item;
                    return rest;
                  })
                )
              });
              source = searchResults.source;
              // Khi có tool call, thêm kết quả vào prompt và gọi lại askGroq
              if (toolResults.length > 0) {
                promptContext = promptContext.concat(toolResults);
                await askGroq(model);
              } else {
                emitEndMessage();
              }
            }
          }
        } else {
          if (response.trim()) {
            emitEndMessage();
          } else {
            await askGroq(model);
          }
        }
      } else {
        await askGroq(model);
      }
    } catch (err) {
      if (err.error.code === 'tool_use_failed') {
        await askGroq(model);
      } else if (err.error.code === 'rate_limit_exceeded') {
        handleError(err, 'Bạn đang hỏi quá nhiều trong thời gian này. Bạn vui lòng đợi ít phút rồi thử lại. Xin cảm ơn.');
      } else {
        handleError(err);
      }
    }
  };

  const handleError = (err, msg = messageError) => {
    error = err;
    response = msg;
    emitEndMessage();
  };

  // --- Xây dựng promptContext từ lịch sử hội thoại ---
  let promptContext = [{ role: 'user', content: text }];
  try {
    // Kiểm tra các tham số bắt buộc
    if (!deviceId || !serverChatbot || !conversationId || !modelId) {
      throw new Error(`code: ${CONSTANTS.CODE.WRONG_PARAMS}`);
    }

    // Lấy thông tin model
    if (modelId) {
      const modelDoc = await Model.findOne({ _id: modelId }, 'name url provider modelUse useGroq reasoning')
        .lean()
        .exec();
      if (!modelDoc || !modelDoc.url) throw new Error('Model not found');
      objModel = modelDoc;
      serverChatbot = objModel.url;
    }

    // Kiểm tra sự tồn tại của conversation và cập nhật updatedAt
    const conv = await Conversation.findOneAndUpdate(
      { _id: conversationId },
      { updatedAt: Date.now() }
    )
      .lean()
      .exec();
    if (!conv) throw new Error(`Conversation not found`);
    conversation = conv;

    // Lấy 5 chat gần nhất làm context
    const chats = await Chat.find({ conversation: conversationId })
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec();
    if (chats.length) {
      const context = chats
        .reverse()
        .map(chat => ({
          role: 'user',
          content: chat.summaryQuestion ? chat.summaryQuestion.trim() : chat.question.trim()
        }))
        .flatMap((userMessage, index) => [
          userMessage,
          {
            role: 'assistant',
            content: chats[index].summaryAnswer ? chats[index].summaryAnswer.trim() : chats[index].answer.trim()
          }
        ]);
      promptContext = context.concat(promptContext);
    }

    // Nếu provider không phải 'heyu' hoặc dùng Groq, thêm system prompt và gọi askGroq
    console.log('haha:objModel.provider', objModel.provider, objModel.useGroq)
    if (objModel.provider === 'vietanh') {
      askVietAnh();
    } else if (objModel.provider !== 'heyu' || objModel.useGroq) {
      promptContext.unshift({ role: 'system', content: systemPrompt });
      askGroq(objModel.modelUse ? objModel.modelUse : objModel, true);
    } else {
      ask();
    }
    res.json({ code: CONSTANTS.CODE.SUCCESS });
  } catch (err) {
    console.error(err);
    res.json({ code: CONSTANTS.CODE.SYSTEM_ERROR, message: MESSAGES.SYSTEM.ERROR });
  }
};
