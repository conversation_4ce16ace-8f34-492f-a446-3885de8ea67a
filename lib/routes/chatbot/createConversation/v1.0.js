const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Conversation = require('../../../models/conversation')
const Chat = require('../../../models/chat')

module.exports = (req, res) => {

  const deviceId = req.body.deviceId;
  const forceCreate = req.body.forceCreate;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversationId
  const oneHourInMs = ms('1h');

  console.log('haha:deviceId', deviceId)

  const checkParams = (next) => {
    if(!deviceId || !serverChatbot){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const getLastConversation = (next) => {
    if (forceCreate) {
      return next();
    }

    Conversation
      .findOne({
        member: deviceId,
        inactive:{
          $ne: true
        }
      }, 'updatedAt')
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        const now = Date.now();
        if (data && data.updatedAt) {
          let diff = now - data.updatedAt;
          if (diff < oneHourInMs) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: data._id
            })
          }
        }

        next();
      })
  }

  const getLastChat = (next) => {
    if (forceCreate) {
      return next();
    }

    Chat
      .findOne({
        member: deviceId
      }, 'conversation updatedAt')
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        const now = Date.now();
        if (data && data.updatedAt) {
          let diff = now - data.updatedAt;
          if (diff < oneHourInMs) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: data.conversation
            })
          }
        }

        next();
      })
  }

  const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: deviceId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            next();
          } else {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS
            })
          }
        })
        .catch((error) => {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            error: error
          })
        })
  }

  const createCollection = (next) => {
    Conversation
    .create({
      id: conversationId,
      member: deviceId
    },(err,result) => {
      return next({
        code: 200,
        data: result._id
      })
    })
  }


  async.waterfall([
    checkParams,
    getLastConversation,
    getLastChat,
    // createConversation,
    createCollection
  ], (err, data) => {
    console.log('haha:err', err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
