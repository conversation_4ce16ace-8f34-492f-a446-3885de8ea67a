const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const Model = require('../../../models/model')

module.exports = (req, res) => {

  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;
  const model = req.body.model || '';

  let serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let text = _.get(req, 'body.text', '');
  let response
  const messageError = 'Xin lỗi, chatbot đang gặp sự cố kết nối. Bạn vui lòng thử lại sau. Xin cảm ơn'

  const checkParams = (next) => {
    if (!deviceId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getModel = (next) => {
    if (!model) {
      return next();
    }

    Model
      .findOne({
        _id: model
      }, 'url')
      .lean()
      .exec((err, result) => {
        if (result && result.url) {
          serverChatbot = result.url;
        }

        next();
      })
  }

  const checkConversationExists = (next) => {
    Conversation
      .findOne({
        _id: conversationId
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        conversation = data

        next();
      })
  }

  const ask = (next) => {
    const options = {
      method: "POST",
      uri: `${serverChatbot}/generate`,
      body: {
        prompt: text
      },
      timeout: 25000,
      json: true, // Automatically stringifies the body to JSON
    }

    rp(options)
      .then((result) => {
        if (result) {
          response = result
        } else {
          response = messageError
        }

        next();
      })
      .catch((error) => {
        response = messageError

        next();
      })
  }

  const createCollection = (next) => {
    Chat
      .create({
        member: deviceId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response
      }, (err, result) => {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }


  async.waterfall([
    checkParams,
    getModel,
    checkConversationExists,
    ask,
    createCollection
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
