const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema

const ModelSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    url: {
      type: String,
    },
    provider: {
      type: String,
      default: 'heyu'
    },
    ownedBy: {
      type: String
    },
    contextWindow: {
      type: Number
    },
    status: {
      type: Number,
      default: 1
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("model", ModelSchema);
