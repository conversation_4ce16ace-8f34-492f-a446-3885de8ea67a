const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const ConversationSchema = new mongoose.Schema(
 {
  member: {
   type: String,
  },
  id: {
    type: Number,
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  inactive:{
    type: Boolean,
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Conversation", ConversationSchema)
