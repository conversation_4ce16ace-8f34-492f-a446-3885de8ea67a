const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatSchema = new mongoose.Schema(
 {
  member: {
   type: String,
  },
  conversation: {
    type: Schema.Types.ObjectId,
  },
  question: {
    type: String,
  },
  reasoning: {
    type: String,
  },
  textReasoning: {
    type: String,
  },
  answer: {
    type: String,
  },
  summaryQuestion: {
    type: String,
  },
  summaryAnswer: {
    type: String,
  },
  error: {
    type: String,
  },
  model: {
    type: Schema.Types.ObjectId,
    ref: 'model'
  },
  source: {
    type: String
  },
  resultsSearch: {
    type: Schema.Types.Mixed
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chat", ChatSchema);
