const express = require('express');
const cachegoose = require('cachegoose');
var cors = require('cors')

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.tempData = {};

global.logger = Logger(`${__dirname}/logs`);

fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});
// Caching

// Middleware
const bodyParser = require('body-parser');

// Handle routes
// const OrderTypeHandle = require("./lib/routes/orderType")
const UtilHandle = require('./lib/routes/util');
const ChatbotHandle = require('./lib/routes/chatbot');
const ModelHandle = require('./lib/routes/model');
const GroqHandle = require('./lib/routes/groq');

// Start server, socket
const app = express();
const server = require('http').Server(app);
global.io = require('socket.io')(server);

app.use(bodyParser.json({limit: '200mb'}));
app.use(express.static('public'))
app.use(cors())

const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }
  // if (!_.isEmpty(middlewares)) {
  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
  // } else {
  //   Object.keys(destinationRoute).forEach((version) => {
  //     app[method](`/api/${version}${routeName}`, destinationRoute[version]);
  //   });
  // }
};

// declareRoute('post','/user/login',null,null,UserHandle.login)
// Object.keys(UserHandle.login).forEach((version) => {
//   app.post(`/${version}/user/login`, UserHandle.login[version]);
// });

//chatbot
declareRoute('post', '/chatbot/create-conversation', [], ChatbotHandle.createConversation);
declareRoute('post', '/chatbot/ask', [], ChatbotHandle.ask);
declareRoute('post', '/chatbot/ask-stream', [], ChatbotHandle.askStream);
declareRoute('post', '/chatbot/list-chat', [], ChatbotHandle.listChat);
declareRoute('post', '/chatbot/list-ask-default', [], ChatbotHandle.listAskDefault);
declareRoute('post', '/chatbot/list-conversation', [], ChatbotHandle.listConversation);
declareRoute('post', '/chatbot/speech-to-text', [], ChatbotHandle.speechToText);
declareRoute('post', '/chatbot/delete-conversation', [], ChatbotHandle.deleteConversation);
declareRoute('post', '/model/create', [], ModelHandle.create);
declareRoute('post', '/model/modify', [], ModelHandle.modify);
declareRoute('post', '/model/list', [], ModelHandle.list);
declareRoute('post', '/model/inactive', [], ModelHandle.inactive);
declareRoute('post', '/groq/sync-model', [], GroqHandle.syncModel);

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

io.on('connection', function (socket) {
  console.log('socket:connected');
  socket.on('join', (roomId, cb) => {
    console.log('socket:join', roomId);
    socket.join(roomId);
  });

  socket.on('leave', (roomId, cb) => {
    console.log('socket:leave', roomId);
    socket.leave(roomId);
  });

  socket.on('disconnect', (reason) => {
    console.log('socket:disconnected', reason);
  });
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});